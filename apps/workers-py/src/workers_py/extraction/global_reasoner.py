"""Global reasoning stage that enforces semantic constraints and builds the final payload."""

from __future__ import annotations

import logging
import math
import re
from dataclasses import dataclass
from decimal import Decimal, InvalidOperation
from typing import Dict, Iterable, List, Optional, Tuple

from ..models import (
    ConfidenceBreakdown,
    CustomerInfo,
    ExtractionResult,
    InvoiceInfo,
    LineItem,
    PagePreview,
    PaymentInstructions,
    RegionBoundingBox,
    RegionCandidateView,
    RegionView,
    SupplierInfo,
    TotalsInfo,
    VatRate,
)
from .confidence import ConfidenceCalculator
from .models import OcrResult, RegionClassification
from .semantic_classifier import VAT_BE, IBAN, BIC, STRUCT_REF, DATE, AMOUNT

logger = logging.getLogger(__name__)


@dataclass
class GlobalReasonerResult:
    extraction: ExtractionResult
    field_confidences: Dict[str, float]


class GlobalReasoner:
    """Combines regional classifications into a coherent extraction result."""

    def __init__(self, confidence_calculator: Optional[ConfidenceCalculator] = None):
        self.confidence_calculator = confidence_calculator or ConfidenceCalculator()

    def assemble(self, ocr: OcrResult, regions: Iterable[RegionClassification]) -> GlobalReasonerResult:
        region_list = list(regions)
        label_map: Dict[str, List[RegionClassification]] = {}
        for classification in region_list:
            label_map.setdefault(classification.label, []).append(classification)

        # Sort regions by score for deterministic selection
        for candidates in label_map.values():
            candidates.sort(key=lambda c: c.score, reverse=True)

        supplier_region = self._select_supplier(label_map.get("SUPPLIER", []))
        customer_region = self._select_customer(label_map.get("CUSTOMER", []))
        payment_region = self._select_payment(label_map.get("PAYMENT", []))
        totals_region = self._select_totals(label_map.get("TOTALS", []))
        meta_regions = label_map.get("META", [])

        supplier = self._extract_supplier(supplier_region, ocr.text)
        customer = self._extract_customer(customer_region)
        payment = self._extract_payment(payment_region, ocr.text)
        totals = self._extract_totals(totals_region, ocr.text)
        invoice = self._extract_invoice(meta_regions, ocr.text)

        self._backfill_missing_invoice(invoice, meta_regions, ocr.text)
        self._backfill_totals_from_invoice(invoice, totals)

        lines = self._build_line_items(totals)

        field_confidences = self._build_confidences(
            supplier_region,
            customer_region,
            payment_region,
            totals_region,
            meta_regions,
            invoice,
            totals,
            payment,
            supplier,
            customer,
        )

        confidence_breakdown = self.confidence_calculator.compute(field_confidences)

        extraction = ExtractionResult(
            supplier=supplier,
            customer=customer,
            invoice=invoice,
            totals=totals,
            payment_instructions=payment,
            lines=lines,
            confidence=confidence_breakdown.document,
            confidence_breakdown=confidence_breakdown,
            regions=self._build_region_views(region_list, supplier_region, customer_region, payment_region, totals_region, meta_regions),
            candidates=self._build_candidates(label_map),
            page_previews=[
                PagePreview(page=page.number, width=page.width, height=page.height, image_base64=page.preview_base64)
                for page in ocr.pages
            ],
        )

        return GlobalReasonerResult(extraction=extraction, field_confidences=confidence_breakdown.fields)

    # ---------------------------------------------------------------------
    # Selection helpers
    # ---------------------------------------------------------------------

    def _select_supplier(self, candidates: List[RegionClassification]) -> Optional[RegionClassification]:
        prioritized = [
            c
            for c in candidates
            if (c.features.get("vat_hits", 0) > 0 or c.features.get("legal_form_hits", 0) > 0)
            and c.features.get("payment_keywords", 0) < 2
        ]
        if prioritized:
            return prioritized[0]
        return candidates[0] if candidates else None

    def _select_customer(self, candidates: List[RegionClassification]) -> Optional[RegionClassification]:
        prioritized = [c for c in candidates if c.features.get("customer_keywords", 0) > 0]
        if prioritized:
            return prioritized[0]
        return candidates[0] if candidates else None

    def _select_payment(self, candidates: List[RegionClassification]) -> Optional[RegionClassification]:
        prioritized = [c for c in candidates if c.features.get("iban_hits", 0) > 0]
        if prioritized:
            return prioritized[0]
        return candidates[0] if candidates else None

    def _select_totals(self, candidates: List[RegionClassification]) -> Optional[RegionClassification]:
        prioritized = [
            c
            for c in candidates
            if c.features.get("totals_keywords", 0) > 0 and c.features.get("amount_hits", 0) >= 2
        ]
        if prioritized:
            return prioritized[0]
        return candidates[0] if candidates else None

    # ---------------------------------------------------------------------
    # Extraction helpers
    # ---------------------------------------------------------------------

    def _extract_supplier(self, classification: Optional[RegionClassification], document_text: str) -> SupplierInfo:
        if not classification:
            return SupplierInfo(name="", vat=self._find_first_vat(document_text), address=None, iban=None)

        text = classification.region.text
        fields = classification.extracted_fields.get("supplier", {})
        name = fields.get("name") or self._first_line(text)
        vat = fields.get("vat") or self._find_first_vat(text)
        address = fields.get("address") or self._infer_address(text)

        return SupplierInfo(
            name=name or "",
            vat=self._normalize_vat(vat),
            address=address,
            iban=None,
        )

    def _extract_customer(self, classification: Optional[RegionClassification]) -> Optional[CustomerInfo]:
        if not classification:
            return None

        text = classification.region.text
        fields = classification.extracted_fields.get("customer", {})
        name = fields.get("name") or self._first_line(text)
        vat = fields.get("vat") or self._find_first_vat(text)
        address = fields.get("address") or self._infer_address(text)

        if not any([name, vat, address]):
            return None

        return CustomerInfo(name=name, vat=self._normalize_vat(vat), address=address)

    def _extract_payment(self, classification: Optional[RegionClassification], document_text: str) -> PaymentInstructions:
        fields: Dict[str, Optional[str]] = {}
        if classification:
            fields = classification.extracted_fields.get("payment_instructions", {})
            region_text = classification.region.text
        else:
            region_text = document_text

        iban = fields.get("iban") or self._find_first(IBAN, region_text)
        bic = fields.get("bic") or self._find_first(BIC, region_text)
        structured = fields.get("structured_ref") or self._find_first(STRUCT_REF, region_text)

        return PaymentInstructions(
            iban=self._normalize_iban(iban),
            bic=self._normalize_bic(bic),
            structured_ref=self._normalize_structured(structured),
        )

    def _extract_totals(self, classification: Optional[RegionClassification], document_text: str) -> TotalsInfo:
        fields: Dict[str, Optional[str]] = {}
        if classification:
            fields = classification.extracted_fields.get("totals", {})
            text = classification.region.text
        else:
            text = document_text

        net = fields.get("net")
        vat = fields.get("vat")
        gross = fields.get("gross")
        currency = fields.get("currency")

        amounts = self._find_amounts(text)
        if not net and amounts:
            net = amounts.get("net")
        if not vat and amounts:
            vat = amounts.get("vat")
        if not gross and amounts:
            gross = amounts.get("gross")
        if not currency and "€" in text:
            currency = "EUR"

        return TotalsInfo(
            net=self._format_decimal(net),
            vat=self._format_decimal(vat),
            gross=self._format_decimal(gross),
            currency=currency or ("EUR" if "€" in document_text else None),
        )

    def _extract_invoice(self, meta_regions: List[RegionClassification], document_text: str) -> InvoiceInfo:
        number = None
        issue_date = None
        due_date = None
        currency = "EUR" if "€" in document_text.lower() else None

        for classification in meta_regions:
            fields = classification.extracted_fields.get("invoice", {})
            number = number or fields.get("number") or self._extract_invoice_number(classification.region.text)
            issue_date = issue_date or self._find_date_near_keyword(classification.region.text, ["issue", "datum", "date"])
            due_date = due_date or self._find_date_near_keyword(classification.region.text, ["due", "verval", "éché"])

        if not number:
            number = self._extract_invoice_number(document_text)
        if not issue_date:
            issue_date = self._find_any_date(document_text)

        return InvoiceInfo(
            number=number,
            issue_date=issue_date,
            due_date=due_date,
            currency=currency,
        )

    def _backfill_missing_invoice(self, invoice: InvoiceInfo, meta_regions: List[RegionClassification], text: str) -> None:
        if not invoice.number:
            invoice.number = self._extract_invoice_number(text)
        if not invoice.issue_date:
            invoice.issue_date = self._find_any_date(text)
        if not invoice.due_date:
            invoice.due_date = self._find_date_near_keyword(text, ["due", "verval", "éché"])

    def _backfill_totals_from_invoice(self, invoice: InvoiceInfo, totals: TotalsInfo) -> None:
        invoice.net = invoice.net or totals.net
        invoice.vat = invoice.vat or totals.vat
        invoice.gross = invoice.gross or totals.gross
        if totals.currency and not invoice.currency:
            invoice.currency = totals.currency
        if invoice.currency and not totals.currency:
            totals.currency = invoice.currency

    def _build_line_items(self, totals: TotalsInfo) -> List[LineItem]:
        if totals.net and totals.vat:
            try:
                net = Decimal(totals.net)
                vat = Decimal(totals.vat)
                vat_rate = self._guess_vat_rate(net, vat)
            except (InvalidOperation, TypeError):
                vat_rate = VatRate.STANDARD
        else:
            vat_rate = VatRate.STANDARD

        if totals.net:
            net_value = totals.net
        elif totals.gross:
            net_value = totals.gross
        else:
            net_value = "0.00"

        return [
            LineItem(
                description="Invoice total",
                quantity="1",
                unit_price=net_value,
                vat_rate=vat_rate,
                account_hint=None,
            )
        ]

    # ---------------------------------------------------------------------
    # Confidence
    # ---------------------------------------------------------------------

    def _build_confidences(
        self,
        supplier_region: Optional[RegionClassification],
        customer_region: Optional[RegionClassification],
        payment_region: Optional[RegionClassification],
        totals_region: Optional[RegionClassification],
        meta_regions: List[RegionClassification],
        invoice: InvoiceInfo,
        totals: TotalsInfo,
        payment: PaymentInstructions,
        supplier: SupplierInfo,
        customer: Optional[CustomerInfo],
    ) -> Dict[str, float]:
        scores: Dict[str, float] = {}

        if supplier_region:
            scores["supplier.name"] = self.confidence_calculator.field_confidence(
                supplier_region.score, bool(supplier.name)
            )
            scores["supplier.vat"] = self.confidence_calculator.field_confidence(
                supplier_region.score,
                bool(supplier.vat and VAT_BE.fullmatch(supplier.vat.replace(" ", ""))),
            )
        if customer_region and customer:
            scores["customer.name"] = self.confidence_calculator.field_confidence(
                customer_region.score, bool(customer.name)
            )

        if payment_region or payment.iban:
            region_score = payment_region.score if payment_region else 0.45
            scores["payment.iban"] = self.confidence_calculator.field_confidence(
                region_score,
                bool(payment.iban and len(payment.iban) >= 16),
            )

        if totals_region:
            totals_valid = self._validate_totals(totals)
            scores["totals.net"] = self.confidence_calculator.field_confidence(
                totals_region.score, bool(totals.net), 1.0 if totals_valid else 0.0
            )
            scores["totals.vat"] = self.confidence_calculator.field_confidence(
                totals_region.score, bool(totals.vat), 1.0 if totals_valid else 0.0
            )
            scores["totals.gross"] = self.confidence_calculator.field_confidence(
                totals_region.score, bool(totals.gross), 1.0 if totals_valid else 0.0
            )

        if meta_regions:
            best_meta = max(meta_regions, key=lambda c: c.score)
            scores["invoice.number"] = self.confidence_calculator.field_confidence(
                best_meta.score, bool(invoice.number)
            )
            scores["invoice.issue_date"] = self.confidence_calculator.field_confidence(
                best_meta.score, bool(invoice.issue_date)
            )
            scores["invoice.due_date"] = self.confidence_calculator.field_confidence(
                best_meta.score, bool(invoice.due_date)
            )

        return scores

    # ---------------------------------------------------------------------
    # Region + candidate views
    # ---------------------------------------------------------------------

    def _build_region_views(
        self,
        classifications: List[RegionClassification],
        supplier_region: Optional[RegionClassification],
        customer_region: Optional[RegionClassification],
        payment_region: Optional[RegionClassification],
        totals_region: Optional[RegionClassification],
        meta_regions: List[RegionClassification],
    ) -> List[RegionView]:
        selected_map: Dict[str, List[str]] = {}
        if supplier_region:
            selected_map.setdefault(supplier_region.region.id, []).append("supplier")
        if customer_region:
            selected_map.setdefault(customer_region.region.id, []).append("customer")
        if payment_region:
            selected_map.setdefault(payment_region.region.id, []).append("payment")
        if totals_region:
            selected_map.setdefault(totals_region.region.id, []).append("totals")
        for meta in meta_regions:
            selected_map.setdefault(meta.region.id, []).append("meta")

        views: List[RegionView] = []
        for classification in classifications:
            bbox = classification.region.bbox
            views.append(
                RegionView(
                    id=classification.region.id,
                    page=classification.region.page,
                    label=classification.label,
                    score=classification.score,
                    text=classification.region.text,
                    bbox=RegionBoundingBox(x0=bbox.x0, y0=bbox.y0, x1=bbox.x1, y1=bbox.y1),
                    selected_for=selected_map.get(classification.region.id, []),
                )
            )
        return views

    def _build_candidates(self, label_map: Dict[str, List[RegionClassification]]) -> Dict[str, List[RegionCandidateView]]:
        mapping: Dict[str, List[RegionCandidateView]] = {}
        for label, candidates in label_map.items():
            field_key = self._field_key_for_label(label)
            if not field_key:
                continue
            mapping[field_key] = [
                RegionCandidateView(
                    region_id=c.region.id,
                    label=c.label,
                    score=c.score,
                    evidence=c.evidence,
                    data=self._candidate_data_for_label(label, c),
                )
                for c in candidates
            ]
        return mapping

    @staticmethod
    def _field_key_for_label(label: str) -> Optional[str]:
        mapping = {
            "SUPPLIER": "supplier",
            "CUSTOMER": "customer",
            "PAYMENT": "payment",
            "TOTALS": "totals",
            "META": "invoice",
        }
        return mapping.get(label)

    def _candidate_data_for_label(
        self, label: str, classification: RegionClassification
    ) -> Dict[str, str]:
        if label == "SUPPLIER":
            supplier = self._extract_supplier(classification, classification.region.text)
            return {
                "name": supplier.name or "",
                "vat": supplier.vat or "",
                "address": supplier.address or "",
            }
        if label == "CUSTOMER":
            customer = self._extract_customer(classification)
            return {
                "name": customer.name if customer and customer.name else "",
                "vat": customer.vat if customer and customer.vat else "",
                "address": customer.address if customer and customer.address else "",
            }
        if label == "PAYMENT":
            payment = self._extract_payment(classification, classification.region.text)
            return {
                "iban": payment.iban or "",
                "bic": payment.bic or "",
                "structured_ref": payment.structured_ref or "",
            }
        if label == "TOTALS":
            totals = self._extract_totals(classification, classification.region.text)
            return {
                "net": totals.net or "",
                "vat": totals.vat or "",
                "gross": totals.gross or "",
                "currency": totals.currency or "",
            }
        if label == "META":
            invoice = self._extract_invoice([classification], classification.region.text)
            return {
                "number": invoice.number or "",
                "issue_date": invoice.issue_date or "",
                "due_date": invoice.due_date or "",
            }
        return {}

    # ---------------------------------------------------------------------
    # Utility extraction helpers
    # ---------------------------------------------------------------------

    @staticmethod
    def _first_line(text: str) -> Optional[str]:
        for line in text.splitlines():
            cleaned = line.strip()
            if cleaned:
                return cleaned
        return None

    @staticmethod
    def _infer_address(text: str) -> Optional[str]:
        lines = [line.strip() for line in text.splitlines() if line.strip()]
        if len(lines) >= 2:
            return "\n".join(lines[1:])
        return None

    @staticmethod
    def _normalize_vat(value: Optional[str]) -> Optional[str]:
        if not value:
            return None
        digits = re.sub(r"[^0-9]", "", value)
        if digits.startswith("0") and len(digits) == 10:
            digits = digits[1:]
        if len(digits) == 9:
            digits = f"0{digits}"
        if len(digits) != 10:
            return value
        return f"BE{digits}"

    @staticmethod
    def _normalize_iban(value: Optional[str]) -> Optional[str]:
        if not value:
            return None
        return value.replace(" ", "").upper()

    @staticmethod
    def _normalize_bic(value: Optional[str]) -> Optional[str]:
        if not value:
            return None
        return value.replace(" ", "").upper()

    @staticmethod
    def _normalize_structured(value: Optional[str]) -> Optional[str]:
        if not value:
            return None
        cleaned = re.sub(r"\s", "", value)
        if cleaned.startswith("+++") and cleaned.endswith("+++"):
            middle = cleaned[3:-3]
            if len(middle) >= 12:
                return f"+++{middle}+++"
        return value

    @staticmethod
    def _find_first(pattern: re.Pattern[str], text: str) -> Optional[str]:
        match = pattern.search(text)
        return match.group(0) if match else None

    @staticmethod
    def _find_first_vat(text: str) -> Optional[str]:
        match = VAT_BE.search(text)
        return match.group(0) if match else None

    @staticmethod
    def _find_amounts(text: str) -> Dict[str, str]:
        amounts = [m.group(0) for m in AMOUNT.finditer(text)]
        if not amounts:
            return {}
        by_value = sorted({GlobalReasoner._as_decimal_string(a) for a in amounts if a}, reverse=True)
        result: Dict[str, str] = {}
        if by_value:
            result["gross"] = by_value[0]
        if len(by_value) > 1:
            result["net"] = by_value[1]
        if len(by_value) > 2:
            result["vat"] = by_value[2]
        return result

    @staticmethod
    def _as_decimal_string(value: str) -> str:
        cleaned = value.replace("€", "").replace("EUR", "").replace(" ", "")
        cleaned = cleaned.replace(",", ".")
        return cleaned

    @staticmethod
    def _format_decimal(value: Optional[str]) -> Optional[str]:
        if value is None:
            return None
        try:
            dec = Decimal(str(value))
            return f"{dec:.2f}"
        except (InvalidOperation, ValueError):
            return value

    @staticmethod
    def _extract_invoice_number(text: str) -> Optional[str]:
        patterns = [
            r"(?:factuur|invoice|facture|nummer|no\.?|numéro)[:\-\s]*([A-Z0-9\-/\.]{3,})",
            r"\bINV[-/\dA-Z]{4,}\b",
        ]
        lowered = text.lower()
        for pattern in patterns:
            match = re.search(pattern, lowered, re.IGNORECASE)
            if match:
                return match.group(1).strip().upper()
        return None

    @staticmethod
    def _find_any_date(text: str) -> Optional[str]:
        match = DATE.search(text)
        if not match:
            return None
        return GlobalReasoner._normalize_date(match.group(0))

    @staticmethod
    def _find_date_near_keyword(text: str, keywords: Iterable[str]) -> Optional[str]:
        lowered = text.lower()
        for keyword in keywords:
            idx = lowered.find(keyword)
            if idx == -1:
                continue
            window = text[max(0, idx - 20) : idx + 40]
            match = DATE.search(window)
            if match:
                return GlobalReasoner._normalize_date(match.group(0))
        return None

    @staticmethod
    def _normalize_date(value: str) -> Optional[str]:
        value = value.strip()
        for fmt in ("%Y-%m-%d", "%d-%m-%Y", "%d/%m/%Y", "%Y/%m/%d"):
            try:
                from datetime import datetime

                parsed = datetime.strptime(value, fmt)
                return parsed.strftime("%Y-%m-%d")
            except ValueError:
                continue
        return None

    @staticmethod
    def _find_amount_from_text(text: str) -> Optional[str]:
        match = AMOUNT.search(text)
        if match:
            return GlobalReasoner._format_decimal(match.group(0))
        return None

    @staticmethod
    def _guess_vat_rate(net: Decimal, vat: Decimal) -> VatRate:
        if net == 0:
            return VatRate.STANDARD
        rate = (vat / net) * Decimal("100")
        candidates = {
            VatRate.STANDARD: Decimal("21"),
            VatRate.REDUCED: Decimal("12"),
            VatRate.LOW: Decimal("6"),
            VatRate.ZERO: Decimal("0"),
        }
        closest = min(candidates.items(), key=lambda item: abs(item[1] - rate))[0]
        return closest

    @staticmethod
    def _validate_totals(totals: TotalsInfo) -> bool:
        try:
            if not (totals.net and totals.vat and totals.gross):
                return False
            net = Decimal(totals.net)
            vat = Decimal(totals.vat)
            gross = Decimal(totals.gross)
            return abs((net + vat) - gross) <= Decimal("0.02")
        except (InvalidOperation, TypeError):
            return False
