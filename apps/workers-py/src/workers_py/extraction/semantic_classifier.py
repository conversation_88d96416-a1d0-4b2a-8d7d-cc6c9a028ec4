"""Semantic classification for clustered regions."""

from __future__ import annotations

import asyncio
import json
import logging
import math
import re
from dataclasses import dataclass
from typing import Dict, Iterable, List, Optional

from google import generativeai as genai

from ..config import settings
from .models import Region, RegionClassification

logger = logging.getLogger(__name__)


VAT_BE = re.compile(r"\bBE0?\d{9}\b", re.IGNORECASE)
IBAN = re.compile(r"\bBE\d{2}(?:[\s-]?\d{4}){3}\d{2}\b", re.IGNORECASE)
BIC = re.compile(r"\b[A-Z]{4}BE[0-9A-Z]{2}(?:[0-9A-Z]{3})?\b")
STRUCT_REF = re.compile(r"\+{3}[0-9\s]{3,}\+{3}")
DATE = re.compile(r"\b(?:\d{4}[/-]\d{2}[/-]\d{2}|\d{2}[/-]\d{2}[/-]\d{4})\b")
AMOUNT = re.compile(r"(?<![A-Z0-9])(?:€|EUR)?\s*[+-]?\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{2})(?![0-9])")
POSTCODE4 = re.compile(r"\b[1-9][0-9]{3}\b")

CUSTOMER_KEYWORDS = [
    "klant",
    "client",
    "customer",
    "facturé à",
    "facturée à",
    "bill to",
    "to:",
]
PAYMENT_KEYWORDS = [
    "iban",
    "bic",
    "sepa",
    "overschrijving",
    "virement",
    "payment",
    "gestructureerde mededeling",
    "communication structurée",
]
TOTALS_KEYWORDS = [
    "totaal",
    "total",
    "totaux",
    "btw",
    "tva",
    "vat",
    "subtotaal",
    "subtotal",
    "net",
    "bruto",
    "incl.",
]
META_KEYWORDS = [
    "factuur",
    "facture",
    "invoice",
    "nummer",
    "numéro",
    "no.",
    "datum",
    "date",
    "due",
    "vervaldatum",
    "échéance",
]
LEGAL_FORMS = [
    "nv",
    "bv",
    "srl",
    "sa",
    "asbl",
    "vzw",
    "sprl",
    "sc",
    "snc",
]
ADDRESS_KEYWORDS = [
    "straat",
    "laan",
    "avenue",
    "road",
    "weg",
    "avenue",
    "rue",
    "boulevard",
    "plaats",
]

LABELS = ["SUPPLIER", "CUSTOMER", "PAYMENT", "TOTALS", "LINE_ITEMS", "META", "OTHER"]


@dataclass
class HeuristicResult:
    label: str
    probabilities: Dict[str, float]
    features: Dict[str, float]
    score: float
    ambiguous: bool


class GeminiFlashRegionClassifier:
    """Thin wrapper around Gemini 2.5 Flash for ambiguous region labelling."""

    def __init__(self, model_id: str, api_key: str):
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name=model_id)
        self.config = genai.types.GenerationConfig(
            temperature=0.25,
            top_p=0.8,
            top_k=32,
            response_mime_type="application/json",
            response_schema={
                "type": "object",
                "required": ["label"],
                "properties": {
                    "label": {
                        "type": "string",
                        "enum": LABELS,
                    },
                    "supplier": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "vat": {"type": "string"},
                            "address": {"type": "string"},
                        },
                    },
                    "customer": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "vat": {"type": "string"},
                            "address": {"type": "string"},
                        },
                    },
                    "payment_instructions": {
                        "type": "object",
                        "properties": {
                            "iban": {"type": "string"},
                            "bic": {"type": "string"},
                            "structured_ref": {"type": "string"},
                        },
                    },
                    "invoice": {
                        "type": "object",
                        "properties": {
                            "number": {"type": "string"},
                            "issue_date": {"type": "string"},
                            "due_date": {"type": "string"},
                        },
                    },
                    "totals": {
                        "type": "object",
                        "properties": {
                            "net": {"type": "number"},
                            "vat": {"type": "number"},
                            "gross": {"type": "number"},
                            "currency": {"type": "string"},
                        },
                    },
                    "evidence": {
                        "type": "array",
                        "items": {"type": "string"},
                    },
                },
            },
        )

    async def classify(self, region: Region, features: Dict[str, float], heuristic_score: float) -> Optional[RegionClassification]:
        prompt = self._build_prompt(region.text)

        async def _call_model() -> Optional[RegionClassification]:
            response = self.model.generate_content(
                [
                    {
                        "mime_type": "image/png",
                        "data": region.image_bytes,
                    },
                    prompt,
                ],
                generation_config=self.config,
            )

            if not response.text:
                return None

            payload = json.loads(response.text)
            label = payload.get("label", "OTHER")
            evidence = payload.get("evidence") or []

            extracted_fields: Dict[str, Dict[str, str]] = {}
            for key in ("supplier", "customer", "payment_instructions", "invoice", "totals"):
                value = payload.get(key)
                if isinstance(value, dict):
                    extracted_fields[key] = {k: str(v) for k, v in value.items() if v not in ("", None)}

            score = min(0.98, 0.55 + 0.4 * heuristic_score)
            probabilities = {lbl: (0.9 if lbl == label else (0.1 / (len(LABELS) - 1))) for lbl in LABELS}

            classification = RegionClassification(
                region=region,
                label=label,
                score=score,
                probabilities=probabilities,
                evidence=evidence,
                extracted_fields=extracted_fields,
                features=features,
                source="gemini",
            )
            return classification

        loop = asyncio.get_running_loop()
        try:
            return await loop.run_in_executor(None, _call_model)
        except Exception as err:  # noqa: BLE001
            logger.warning("Gemini classification failed for region %s: %s", region.id, err)
            return None

    @staticmethod
    def _build_prompt(text: str) -> str:
        snippet = text.strip()
        if not snippet:
            snippet = "<no text from OCR>"
        return (
            "You are labeling a single region from an invoice scan. Do not assume where anything should be on the page. "
            "Decide between {SUPPLIER, CUSTOMER, PAYMENT, TOTALS, LINE_ITEMS, META, OTHER} using only the content.\n"
            "Rules:\n"
            "– If the text is dominated by IBAN/BIC/SEPA/transfer terms, it’s PAYMENT.\n"
            "– If it contains a Belgian VAT (BE0#########) or legal form (NV/BV/SRL/SA/ASBL/VZW) and looks like a company identity block (name + address lines), it’s SUPPLIER unless explicitly labeled Klant/Client/Customer.\n"
            "– If it includes “Klant/Client/Customer/Facturé à/Bill To”, it’s CUSTOMER.\n"
            "– If it includes totals language (Total/Totaal/TVA/BTW/Subtotal) with amounts, it’s TOTALS.\n"
            "– If it includes invoice number/date/due cues, it’s META.\n"
            "Extract only fields that are clearly present; otherwise leave them empty. Return JSON per schema.\n\n"
            f"Region text:\n{snippet}"
        )


class RegionSemanticClassifier:
    """Hybrid scorer that combines heuristics with Gemini for ambiguous regions."""

    def __init__(self, gemini_classifier: Optional[GeminiFlashRegionClassifier] = None):
        self.gemini_classifier = gemini_classifier

    async def classify(self, regions: Iterable[Region]) -> List[RegionClassification]:
        classifications: List[RegionClassification] = []
        for region in regions:
            heuristic = self._score_region(region)
            classification = RegionClassification(
                region=region,
                label=heuristic.label,
                score=heuristic.score,
                probabilities=heuristic.probabilities,
                evidence=[],
                extracted_fields={},
                features=heuristic.features,
                source="heuristic",
            )

            if heuristic.ambiguous and self.gemini_classifier:
                gemini_result = await self.gemini_classifier.classify(
                    region, heuristic.features, heuristic.score
                )
                if gemini_result:
                    classification = gemini_result

            classifications.append(classification)
        return classifications

    def _score_region(self, region: Region) -> HeuristicResult:
        text = region.text
        lowered = text.lower()

        features: Dict[str, float] = {
            "vat_hits": len(VAT_BE.findall(text)),
            "iban_hits": len(IBAN.findall(text)),
            "bic_hits": len(BIC.findall(text)),
            "structured_ref_hits": len(STRUCT_REF.findall(text)),
            "date_hits": len(DATE.findall(text)),
            "amount_hits": len(AMOUNT.findall(text)),
            "postcode_hits": len(POSTCODE4.findall(text)),
            "customer_keywords": self._keyword_hits(lowered, CUSTOMER_KEYWORDS),
            "payment_keywords": self._keyword_hits(lowered, PAYMENT_KEYWORDS),
            "totals_keywords": self._keyword_hits(lowered, TOTALS_KEYWORDS),
            "meta_keywords": self._keyword_hits(lowered, META_KEYWORDS),
            "legal_form_hits": self._keyword_hits(lowered, LEGAL_FORMS),
            "address_keywords": self._keyword_hits(lowered, ADDRESS_KEYWORDS),
            "line_count": float(len(region.line_ids)),
        }

        scores = {label: 0.0 for label in LABELS}

        scores["SUPPLIER"] += 0.45 * features["vat_hits"]
        scores["SUPPLIER"] += 0.25 * features["legal_form_hits"]
        scores["SUPPLIER"] += 0.18 * min(features["address_keywords"], 2)
        scores["SUPPLIER"] += 0.15 * features["postcode_hits"]
        if features["customer_keywords"] == 0 and features["payment_keywords"] < 2:
            scores["SUPPLIER"] += 0.1 * min(features["line_count"], 4)

        scores["CUSTOMER"] += 0.55 * features["customer_keywords"]
        scores["CUSTOMER"] += 0.20 * min(features["address_keywords"], 2)
        scores["CUSTOMER"] += 0.15 * features["postcode_hits"]

        scores["PAYMENT"] += 0.6 * (features["iban_hits"] + features["bic_hits"])
        scores["PAYMENT"] += 0.35 * features["payment_keywords"]
        scores["PAYMENT"] += 0.25 * features["structured_ref_hits"]
        scores["PAYMENT"] += 0.1 * min(features["amount_hits"], 3)

        scores["TOTALS"] += 0.45 * features["totals_keywords"]
        scores["TOTALS"] += 0.25 * min(features["amount_hits"], 4)
        scores["TOTALS"] += 0.15 * features["date_hits"]

        scores["META"] += 0.45 * features["meta_keywords"]
        scores["META"] += 0.35 * features["date_hits"]
        scores["META"] += 0.2 * min(features["amount_hits"], 2)

        scores["LINE_ITEMS"] += 0.2 * min(features["line_count"], 6)
        if "qty" in lowered or "quantity" in lowered:
            scores["LINE_ITEMS"] += 0.3
        if any(token in lowered for token in ("unit", "prijs", "price", "htva")):
            scores["LINE_ITEMS"] += 0.3

        # Normalize via softmax to probabilities
        probabilities = self._softmax(scores)
        label, score = max(probabilities.items(), key=lambda item: item[1])
        sorted_probs = sorted(probabilities.values(), reverse=True)
        second_best = sorted_probs[1] if len(sorted_probs) > 1 else 0.0
        ambiguous = (score - second_best) <= 0.15 or score < 0.45

        return HeuristicResult(
            label=label,
            probabilities=probabilities,
            features=features,
            score=score,
            ambiguous=ambiguous,
        )

    @staticmethod
    def _keyword_hits(text: str, keywords: Iterable[str]) -> int:
        return sum(1 for kw in keywords if kw in text)

    @staticmethod
    def _softmax(scores: Dict[str, float]) -> Dict[str, float]:
        values = list(scores.values())
        max_score = max(values) if values else 0.0
        exp_scores = [math.exp(v - max_score) for v in values]
        total = sum(exp_scores) or 1.0
        return {label: exp_scores[i] / total for i, label in enumerate(scores.keys())}


def create_semantic_classifier() -> RegionSemanticClassifier:
    """Factory that wires Gemini config from settings."""
    gemini_classifier: Optional[GeminiFlashRegionClassifier] = None
    if settings.GEMINI_API_KEY:
        gemini_classifier = GeminiFlashRegionClassifier(
            model_id=settings.GEMINI_MODEL,
            api_key=settings.GEMINI_API_KEY,
        )
    else:
        logger.warning("Gemini API key missing – ambiguous regions will rely on heuristics only")
    return RegionSemanticClassifier(gemini_classifier=gemini_classifier)

