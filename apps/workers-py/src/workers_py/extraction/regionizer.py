"""Regionizer groups OCR lines into semantic regions without positional priors."""

from __future__ import annotations

from dataclasses import dataclass
from typing import Iterable, List

from .models import BoundingBox, OcrLine, OcrResult, Region


@dataclass
class RegionizerConfig:
    """Configuration knobs for whitespace-based region clustering."""

    vertical_gap_threshold: float = 0.035  # relative to page height
    horizontal_shift_threshold: float = 0.18  # normalized distance to start new block


class Regionizer:
    """Cluster OCR lines into independent regions using whitespace heuristics."""

    def __init__(self, config: RegionizerConfig | None = None):
        self.config = config or RegionizerConfig()

    def build_regions(self, ocr: OcrResult) -> List[Region]:
        """Cluster lines into regions using an XY-cut style grouping."""
        regions: List[Region] = []

        for page in ocr.pages:
            page_lines = [line for line in ocr.lines if line.page == page.number]
            if not page_lines:
                continue

            page_lines.sort(key=lambda ln: (ln.bbox.y0, ln.bbox.x0))
            current_group: List[OcrLine] = []

            for line in page_lines:
                if not current_group:
                    current_group.append(line)
                    continue

                if self._should_start_new_region(current_group, line):
                    regions.append(self._build_region(ocr, page.number, current_group, len(regions)))
                    current_group = [line]
                else:
                    current_group.append(line)

            if current_group:
                regions.append(self._build_region(ocr, page.number, current_group, len(regions)))

        return regions

    def _should_start_new_region(self, current_group: List[OcrLine], candidate: OcrLine) -> bool:
        """Decide whether a new region should start before `candidate`."""
        last_line = current_group[-1]
        vertical_gap = candidate.bbox.y0 - last_line.bbox.y1

        if vertical_gap > self.config.vertical_gap_threshold:
            return True

        region_min_x = min(line.bbox.x0 for line in current_group)
        region_max_x = max(line.bbox.x1 for line in current_group)
        cand_center = (candidate.bbox.x0 + candidate.bbox.x1) / 2

        if cand_center < region_min_x - self.config.horizontal_shift_threshold:
            return True
        if cand_center > region_max_x + self.config.horizontal_shift_threshold:
            return True

        return False

    def _build_region(
        self, ocr: OcrResult, page_number: int, lines: Iterable[OcrLine], global_index: int
    ) -> Region:
        """Construct a Region instance from clustered lines."""
        line_list = list(lines)
        token_ids = [token_id for line in line_list for token_id in line.token_ids]
        bbox = BoundingBox(
            x0=min(line.bbox.x0 for line in line_list),
            y0=min(line.bbox.y0 for line in line_list),
            x1=max(line.bbox.x1 for line in line_list),
            y1=max(line.bbox.y1 for line in line_list),
        )

        page = ocr.get_page(page_number)
        if not page:
            raise ValueError(f"Missing page metadata for page {page_number}")

        padded_bbox = bbox.padded(pad=0.01)
        from io import BytesIO
        from PIL import Image

        image = Image.open(BytesIO(page.image_bytes))
        if image.mode != "RGB":
            image = image.convert("RGB")

        left = int(padded_bbox.x0 * image.width)
        top = int(padded_bbox.y0 * image.height)
        right = int(padded_bbox.x1 * image.width)
        bottom = int(padded_bbox.y1 * image.height)
        crop = image.crop((left, top, right, bottom))

        buffer = BytesIO()
        crop.save(buffer, format="PNG")
        crop_bytes = buffer.getvalue()

        region_id = f"p{page_number}_r{global_index + 1}"
        text = "\n".join(line.text for line in line_list)

        return Region(
            id=region_id,
            page=page_number,
            bbox=bbox,
            text=text,
            line_ids=[line.id for line in line_list],
            token_ids=token_ids,
            image_bytes=crop_bytes,
        )

