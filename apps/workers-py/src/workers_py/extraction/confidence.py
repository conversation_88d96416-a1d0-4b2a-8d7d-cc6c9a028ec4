"""Confidence scoring utilities for extraction fields."""

from __future__ import annotations

from dataclasses import dataclass, field
from typing import Dict


@dataclass
class ConfidenceResult:
    document: float
    fields: Dict[str, float] = field(default_factory=dict)


class ConfidenceCalculator:
    """Combines region classification scores with validation signals."""

    def __init__(self, document_weights: Dict[str, float] | None = None):
        self.document_weights = document_weights or {
            "supplier.name": 1.0,
            "supplier.vat": 0.8,
            "invoice.number": 1.0,
            "invoice.issue_date": 0.9,
            "invoice.due_date": 0.5,
            "totals.net": 1.0,
            "totals.vat": 0.8,
            "totals.gross": 1.1,
            "payment.iban": 0.8,
        }

    def field_confidence(
        self,
        label_score: float,
        pattern_valid: bool = False,
        cross_check_score: float = 0.0,
    ) -> float:
        base = max(0.05, min(label_score, 1.0))
        pattern_bonus = 0.25 if pattern_valid else 0.0
        cross_bonus = 0.25 * max(0.0, min(cross_check_score, 1.0))
        confidence = 0.6 * base + pattern_bonus + cross_bonus
        return float(max(0.0, min(confidence, 0.99)))

    def document_confidence(self, field_scores: Dict[str, float]) -> float:
        weighted_product = 1.0
        total_weight = 0.0
        for field, weight in self.document_weights.items():
            score = field_scores.get(field)
            if score is None:
                continue
            weighted_product *= max(0.05, min(score, 0.99)) ** weight
            total_weight += weight
        if total_weight == 0:
            return 0.0
        return float(weighted_product ** (1.0 / total_weight))

    def compute(self, field_scores: Dict[str, float]) -> ConfidenceBreakdown:
        document_score = self.document_confidence(field_scores)
        return ConfidenceBreakdown(document=document_score, fields=field_scores)

