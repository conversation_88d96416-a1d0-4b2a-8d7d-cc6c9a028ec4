"""Pydantic models for request/response validation."""

from datetime import datetime
from enum import Enum
from typing import Any

from pydantic import BaseModel, Field, HttpUrl, field_serializer, validator


class TaskStatus(str, Enum):
    """Task status enumeration."""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    UNKNOWN = "unknown"


class HealthStatus(str, Enum):
    """Health status enumeration."""

    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"


class ProcessingOptions(BaseModel):
    """Optional processing parameters."""

    ocr_language: str = Field(default="eng", description="OCR language code")
    extract_tables: bool = Field(default=False, description="Extract tables from PDF")
    extract_images: bool = Field(default=False, description="Extract images from PDF")
    ai_categorize: bool = Field(default=True, description="Perform AI categorization")
    ai_extract_entities: bool = Field(
        default=True, description="Extract entities with AI"
    )
    confidence_threshold: float = Field(
        default=0.7,
        ge=0.0,
        le=1.0,
        description="Minimum confidence threshold for AI results",
    )
    max_pages: int | None = Field(
        default=None, ge=1, description="Maximum number of pages to process"
    )

    class Config:
        """Pydantic config."""

        extra = "allow"  # Allow additional fields


class DocumentProcessRequest(BaseModel):
    """Request model for document processing."""

    entity_id: str = Field(..., description="Unique identifier for the entity")
    file_url: HttpUrl = Field(..., description="URL to the file to process")
    options: ProcessingOptions | None = Field(
        default=None, description="Optional processing parameters"
    )

    @validator("entity_id")
    def validate_entity_id(cls, v: str) -> str:
        """Validate entity ID format."""
        if not v or not v.strip():
            raise ValueError("Entity ID cannot be empty")
        if len(v.strip()) < 3:
            raise ValueError("Entity ID must be at least 3 characters long")
        return v.strip()

    class Config:
        """Pydantic config."""

        json_schema_extra = {
            "example": {
                "entity_id": "invoice-12345",
                "file_url": "https://example.com/document.pdf",
                "options": {
                    "ocr_language": "eng",
                    "extract_tables": True,
                    "ai_categorize": True,
                    "confidence_threshold": 0.8,
                },
            }
        }


class DocumentProcessResponse(BaseModel):
    """Response model for document processing initiation."""

    task_id: str = Field(..., description="Unique task identifier")
    status: TaskStatus = Field(..., description="Initial task status")
    message: str = Field(..., description="Response message")
    estimated_completion_time: int | None = Field(
        default=None, description="Estimated completion time in seconds"
    )

    class Config:
        """Pydantic config."""

        json_schema_extra = {
            "example": {
                "task_id": "abc123-def456-ghi789",
                "status": "processing",
                "message": "Document processing started successfully",
                "estimated_completion_time": 60,
            }
        }


class AIAnalysisResult(BaseModel):
    """AI analysis result model."""

    document_type: str | None = Field(
        default=None, description="Detected document type"
    )
    confidence: float | None = Field(
        default=None, ge=0.0, le=1.0, description="Confidence score"
    )
    categories: list[str] = Field(
        default_factory=list, description="Document categories"
    )
    entities: dict[str, Any] = Field(
        default_factory=dict, description="Extracted entities"
    )
    summary: str | None = Field(default=None, description="Document summary")
    key_information: dict[str, Any] = Field(
        default_factory=dict, description="Key information extracted"
    )
    language: str | None = Field(default=None, description="Detected language")

    class Config:
        """Pydantic config."""

        json_schema_extra = {
            "example": {
                "document_type": "invoice",
                "confidence": 0.95,
                "categories": ["financial", "accounts_payable"],
                "entities": {
                    "vendor": "Acme Corp",
                    "amount": "1234.56",
                    "date": "2023-12-01",
                    "invoice_number": "INV-001",
                },
                "summary": "Invoice from Acme Corp for $1,234.56",
                "key_information": {
                    "total_amount": 1234.56,
                    "currency": "USD",
                    "due_date": "2023-12-31",
                },
                "language": "en",
            }
        }


class ProcessingMetadata(BaseModel):
    """Document processing metadata."""

    file_size: int | None = Field(default=None, description="File size in bytes")
    file_type: str | None = Field(default=None, description="File MIME type")
    page_count: int | None = Field(default=None, description="Number of pages")
    processing_time: float | None = Field(
        default=None, description="Processing time in seconds"
    )
    ocr_confidence: float | None = Field(
        default=None, description="OCR confidence score"
    )
    extracted_images: int | None = Field(
        default=None, description="Number of extracted images"
    )
    extracted_tables: int | None = Field(
        default=None, description="Number of extracted tables"
    )
    character_count: int | None = Field(
        default=None, description="Total character count"
    )
    word_count: int | None = Field(default=None, description="Total word count")

    class Config:
        """Pydantic config."""

        extra = "allow"


class TaskResult(BaseModel):
    """Complete task result model."""

    entity_id: str = Field(..., description="Entity identifier")
    file_url: str = Field(..., description="Original file URL")
    extracted_text: str = Field(..., description="Extracted text content")
    metadata: ProcessingMetadata = Field(..., description="Processing metadata")
    ai_analysis: AIAnalysisResult = Field(..., description="AI analysis results")
    processing_time: float = Field(..., description="Total processing time")
    timestamp: datetime = Field(
        default_factory=datetime.utcnow, description="Processing timestamp"
    )

    class Config:
        """Pydantic config."""

        json_encoders = {datetime: lambda v: v.isoformat()}


class TaskStatusResponse(BaseModel):
    """Response model for task status queries."""

    task_id: str = Field(..., description="Task identifier")
    status: TaskStatus = Field(..., description="Current task status")
    message: str = Field(..., description="Status message")
    progress: int | None = Field(
        default=None, ge=0, le=100, description="Progress percentage (0-100)"
    )
    result: TaskResult | None = Field(
        default=None, description="Task result if completed"
    )
    error: str | None = Field(default=None, description="Error message if failed")
    created_at: datetime | None = Field(
        default=None, description="Task creation timestamp"
    )
    updated_at: datetime | None = Field(
        default=None, description="Last update timestamp"
    )

    @field_serializer("created_at", "updated_at")
    def _ser_dt(self, v: datetime | None) -> str | None:
        return v.isoformat() if isinstance(v, datetime) else None

    class Config:
        """Pydantic config."""

        json_encoders = {datetime: lambda v: v.isoformat()}
        json_schema_extra = {
            "example": {
                "task_id": "abc123-def456-ghi789",
                "status": "processing",
                "message": "Document is being processed",
                "progress": 75,
                "result": None,
                "error": None,
                "created_at": "2023-12-01T10:00:00Z",
                "updated_at": "2023-12-01T10:02:30Z",
            }
        }


class HealthResponse(BaseModel):
    """Health check response model."""

    status: HealthStatus = Field(..., description="Overall health status")
    version: str = Field(..., description="Application version")
    timestamp: datetime = Field(
        default_factory=datetime.utcnow, description="Health check timestamp"
    )
    services: dict[str, str] = Field(..., description="Service status details")
    uptime: float | None = Field(default=None, description="Uptime in seconds")

    class Config:
        """Pydantic config."""

        json_encoders = {datetime: lambda v: v.isoformat()}
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "version": "0.1.0",
                "timestamp": "2023-12-01T10:00:00Z",
                "services": {
                    "database": "connected",
                    "redis": "connected",
                    "celery": "running",
                },
                "uptime": 86400.0,
            }
        }


class ErrorResponse(BaseModel):
    """Error response model."""

    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: dict[str, Any] | None = Field(
        default=None, description="Additional error details"
    )
    timestamp: datetime = Field(
        default_factory=datetime.utcnow, description="Error timestamp"
    )

    class Config:
        """Pydantic config."""

        json_encoders = {datetime: lambda v: v.isoformat()}


# Database models (for ORM or raw SQL)
class DocumentProcessingResult(BaseModel):
    """Database model for processing results."""

    id: int | None = Field(default=None, description="Primary key")
    entity_id: str = Field(..., description="Entity identifier")
    file_url: str = Field(..., description="File URL")
    task_id: str = Field(..., description="Celery task ID")
    status: TaskStatus = Field(..., description="Processing status")
    extracted_text: str | None = Field(default=None, description="Extracted text")
    metadata: dict[str, Any] | None = Field(
        default=None, description="Processing metadata"
    )
    ai_analysis: dict[str, Any] | None = Field(default=None, description="AI analysis")
    error_message: str | None = Field(
        default=None, description="Error message if failed"
    )
    created_at: datetime = Field(
        default_factory=datetime.utcnow, description="Creation timestamp"
    )
    updated_at: datetime | None = Field(default=None, description="Update timestamp")

    class Config:
        """Pydantic config."""

        from_attributes = True  # For SQLAlchemy compatibility
        json_encoders = {datetime: lambda v: v.isoformat()}


# =============================================================================
# INBOX E2E - Document Processing Models
# =============================================================================


class VatRate(int, Enum):
    """Belgian VAT rates."""

    STANDARD = 21
    REDUCED = 12
    LOW = 6
    ZERO = 0


class DocumentStatus(str, Enum):
    """Document processing status."""

    UPLOADED = "uploaded"
    EXTRACTED = "extracted"
    SUGGESTED = "suggested"
    CONFIRMED = "confirmed"
    POSTED = "posted"
    EXPORTED = "exported"
    FAILED = "failed"


class PartyInfo(BaseModel):
    """Generic party information (supplier or customer)."""

    name: str | None = Field(None, description="Party name")
    vat: str | None = Field(None, description="VAT number (BE)")
    address: str | None = Field(None, description="Postal address")


class SupplierInfo(PartyInfo):
    """Backwards compatible supplier info (IBAN kept for legacy consumers)."""

    iban: str | None = Field(None, description="Legacy IBAN field; prefer payment instructions")


class CustomerInfo(PartyInfo):
    """Customer information mirrors supplier schema without IBAN."""

    pass


class PaymentInstructions(BaseModel):
    """Payment instructions extracted from invoice."""

    iban: str | None = Field(None, description="Payment IBAN")
    bic: str | None = Field(None, description="BIC identifier")
    structured_ref: str | None = Field(None, description="Structured payment reference")


class InvoiceMeta(BaseModel):
    """Invoice metadata (number and dates)."""

    number: str | None = Field(None, description="Invoice number")
    issue_date: str | None = Field(
        None, pattern=r"^\d{4}-\d{2}-\d{2}$", description="Issue date YYYY-MM-DD"
    )
    due_date: str | None = Field(
        None, pattern=r"^\d{4}-\d{2}-\d{2}$", description="Due date YYYY-MM-DD"
    )
    currency: str | None = Field("EUR", description="Currency code")


class TotalsInfo(BaseModel):
    """Monetary totals for net/VAT/gross."""

    net: str | None = Field(None, description="Net amount as decimal string")
    vat: str | None = Field(None, description="VAT amount as decimal string")
    gross: str | None = Field(None, description="Gross amount as decimal string")
    currency: str | None = Field("EUR", description="Currency code")


class InvoiceInfo(InvoiceMeta):
    """Legacy invoice info structure with totals for compatibility."""

    net: str | None = Field(None, description="Net amount as decimal string")
    vat: str | None = Field(None, description="VAT amount as decimal string")
    gross: str | None = Field(None, description="Gross amount as decimal string")


class LineItem(BaseModel):
    """Invoice line item from extraction."""

    description: str = Field(..., min_length=1, description="Line description")
    quantity: str = Field("1", description="Quantity as decimal string")
    unit_price: str = Field(..., description="Unit price as decimal string")
    vat_rate: VatRate = Field(..., description="VAT rate percentage")
    account_hint: int | None = Field(None, description="Suggested account ID")


class RegionBoundingBox(BaseModel):
    """Bounding box for a classified region."""

    x0: float = Field(..., ge=0.0, le=1.0)
    y0: float = Field(..., ge=0.0, le=1.0)
    x1: float = Field(..., ge=0.0, le=1.0)
    y1: float = Field(..., ge=0.0, le=1.0)


class RegionView(BaseModel):
    """Region exposed to the UI for highlighting and review."""

    id: str
    page: int
    label: str
    score: float = Field(..., ge=0.0, le=1.0)
    text: str
    bbox: RegionBoundingBox
    selected_for: list[str] = Field(default_factory=list)


class RegionCandidateView(BaseModel):
    """Candidate region for a particular field."""

    region_id: str
    label: str
    score: float
    evidence: list[str] = Field(default_factory=list)
    data: dict[str, str] = Field(default_factory=dict)


class PagePreview(BaseModel):
    """Lightweight preview image for overlay rendering."""

    page: int
    width: int
    height: int
    image_base64: str


class ConfidenceBreakdown(BaseModel):
    """Confidence information per field and overall."""

    document: float = Field(..., ge=0.0, le=1.0)
    fields: dict[str, float] = Field(default_factory=dict)


class ExtractionResult(BaseModel):
    """Complete AI extraction result matching TypeScript contract."""

    supplier: SupplierInfo = Field(default_factory=lambda: SupplierInfo())
    customer: CustomerInfo | None = Field(default=None)
    invoice: InvoiceInfo = Field(default_factory=lambda: InvoiceInfo())
    totals: TotalsInfo = Field(default_factory=lambda: TotalsInfo())
    payment_instructions: PaymentInstructions = Field(default_factory=lambda: PaymentInstructions())
    lines: list[LineItem] = Field(default_factory=list, description="Invoice line items")
    confidence: float | None = Field(None, ge=0.0, le=1.0, description="Document confidence")
    confidence_breakdown: ConfidenceBreakdown | None = Field(
        None, description="Per-field confidence breakdown"
    )
    regions: list[RegionView] = Field(default_factory=list, description="Regions with labels")
    candidates: dict[str, list[RegionCandidateView]] = Field(
        default_factory=dict, description="Field-level candidates"
    )
    page_previews: list[PagePreview] = Field(
        default_factory=list, description="Preview images for each page"
    )

    class Config:
        """Pydantic config."""

        json_schema_extra = {
            "example": {
                "supplier": {
                    "name": "ACME Office Supplies",
                    "vat": "BE0123456789",
                    "address": "Rue de la Paix 123, 1000 Brussels",
                },
                "customer": {
                    "name": "Client NV",
                    "vat": "BE9999999999",
                },
                "invoice": {
                    "number": "INV-2024-001",
                    "issue_date": "2024-08-26",
                    "due_date": "2024-09-26",
                    "currency": "EUR",
                },
                "totals": {
                    "net": "100.00",
                    "vat": "21.00",
                    "gross": "121.00",
                    "currency": "EUR",
                },
                "payment_instructions": {
                    "iban": "****************",
                    "bic": "BBRUBEBB",
                },
                "lines": [
                    {
                        "description": "Office supplies",
                        "quantity": "1",
                        "unit_price": "100.00",
                        "vat_rate": 21,
                        "account_hint": None,
                    }
                ],
                "confidence": 0.92,
                "confidence_breakdown": {
                    "document": 0.92,
                    "fields": {
                        "supplier.name": 0.95,
                        "invoice.number": 0.93,
                    },
                },
            }
        }
class ProcessDocumentRequest(BaseModel):
    """Request to process a document (from BFF to workers)."""

    document_id: int = Field(..., description="Document ID from database")
    entity_id: int = Field(..., description="Entity ID")
    file_url: HttpUrl = Field(..., description="Signed URL to download file")

    class Config:
        """Pydantic config."""

        json_schema_extra = {
            "example": {
                "document_id": 123,
                "entity_id": 456,
                "file_url": "https://storage.supabase.co/v1/object/sign/inbox/entity/uuid.pdf?token=...",
            }
        }


class ProcessDocumentResponse(BaseModel):
    """Response from document processing initiation."""

    document_id: int = Field(..., description="Document ID")
    task_id: str = Field(..., description="Background task ID")
    status: str = Field("processing", description="Initial status")
    message: str | None = Field(None, description="Optional message")

    class Config:
        """Pydantic config."""

        json_schema_extra = {
            "example": {
                "document_id": 123,
                "task_id": "celery-task-uuid",
                "status": "processing",
                "message": "Document processing started",
            }
        }


class SuggestionLine(BaseModel):
    """Journal line suggestion for posting."""

    account_id: int = Field(..., description="GL account ID")
    debit: str = Field("0", description="Debit amount as decimal string")
    credit: str = Field("0", description="Credit amount as decimal string")
    vat_code_id: int | None = Field(None, description="VAT code ID")
    memo: str | None = Field(None, description="Line description/memo")


class Suggestion(BaseModel):
    """Complete journal suggestion with balanced lines."""

    journal_date: str = Field(
        ..., pattern=r"^\d{4}-\d{2}-\d{2}$", description="Journal date YYYY-MM-DD"
    )
    reference: str | None = Field(None, description="Invoice number or reference")
    description: str = Field(..., min_length=1, description="Journal description")
    lines: list[SuggestionLine] = Field(
        ..., min_length=2, description="Journal lines (at least expense + payable)"
    )
    balance_warning: str | None = Field(None, description="Warning if journal is not perfectly balanced")

    @validator("lines")
    def validate_balanced_lines(
        cls, v: list["SuggestionLine"]
    ) -> list["SuggestionLine"]:
        """Check journal lines balance and add warning if needed."""
        total_debits = sum(float(line.debit) for line in v)
        total_credits = sum(float(line.credit) for line in v)
        difference = abs(total_debits - total_credits)

        # Only fail for major imbalances (> €1.00)
        if difference >= 1.00:
            raise ValueError(
                f"Journal severely unbalanced: debits={total_debits:.2f}, credits={total_credits:.2f}, difference={difference:.2f}"
            )

        return v

    @validator("balance_warning", always=True)
    def check_balance_warning(cls, v, values):
        """Set balance warning if lines are not perfectly balanced."""
        if "lines" in values:
            lines = values["lines"]
            total_debits = sum(float(line.debit) for line in lines)
            total_credits = sum(float(line.credit) for line in lines)
            difference = abs(total_debits - total_credits)

            if difference >= 0.01:
                return f"Journal not perfectly balanced: difference of €{difference:.2f} (debits: €{total_debits:.2f}, credits: €{total_credits:.2f})"

        return v

    class Config:
        """Pydantic config."""

        json_schema_extra = {
            "example": {
                "journal_date": "2024-08-26",
                "reference": "INV-2024-001",
                "description": "Office supplies - ACME Office Supplies",
                "lines": [
                    {
                        "account_id": 6000,  # Office expenses
                        "debit": "100.00",
                        "credit": "0",
                        "vat_code_id": 1,
                        "memo": "Office supplies",
                    },
                    {
                        "account_id": 4510,  # VAT receivable
                        "debit": "21.00",
                        "credit": "0",
                        "memo": "VAT 21%",
                    },
                    {
                        "account_id": 4400,  # Accounts payable
                        "debit": "0",
                        "credit": "121.00",
                        "memo": "ACME Office Supplies",
                    },
                ],
            }
        }


class ExportPayload(BaseModel):
    """Export format for assist mode integrations."""

    entity_id: int = Field(..., description="Entity ID")
    document_id: int = Field(..., description="Document ID")
    supplier: SupplierInfo = Field(..., description="Supplier information")
    lines: list[dict[str, str | int]] = Field(
        ..., min_length=1, description="Export line items"
    )
    total_gross: str = Field(..., description="Total gross amount")
    invoice_number: str = Field(..., description="Invoice number")
    issue_date: str = Field(
        ..., pattern=r"^\d{4}-\d{2}-\d{2}$", description="Issue date"
    )
    due_date: str | None = Field(
        None, pattern=r"^\d{4}-\d{2}-\d{2}$", description="Due date"
    )
    currency: str = Field("EUR", description="Currency code")

    class Config:
        """Pydantic config."""

        json_schema_extra = {
            "example": {
                "entity_id": 456,
                "document_id": 123,
                "supplier": {
                    "name": "ACME Office Supplies",
                    "vat": "BE0123456789",
                    "iban": "BE12 3456 7890 1234",
                },
                "lines": [
                    {
                        "account_code": "6000",
                        "description": "Office supplies",
                        "net": "100.00",
                        "vat_rate": 21,
                        "vat_amount": "21.00",
                    }
                ],
                "total_gross": "121.00",
                "invoice_number": "INV-2024-001",
                "issue_date": "2024-08-26",
                "due_date": "2024-09-26",
                "currency": "EUR",
            }
        }
