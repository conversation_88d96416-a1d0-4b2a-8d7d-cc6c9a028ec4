"""Base extraction adapter interface."""

from abc import ABC, abstractmethod
from typing import Any, Dict, <PERSON><PERSON>

from ..models import ExtractionResult
from ..extraction.models import DocumentContext


class ExtractionAdapter(ABC):
    """Abstract base class for extraction adapters."""

    @abstractmethod
    async def extract(self, document: DocumentContext) -> Tuple[ExtractionResult, float, Dict[str, Any]]:
        """
        Extract structured data from the document context.

        Args:
            document: OCR context including text, tokens, and metadata

        Returns:
            Tuple of (extraction_result, confidence, metadata)
            - extraction_result: ExtractionResult with supplier, invoice, and line data
            - confidence: Float between 0.0 and 1.0 indicating extraction quality
            - metadata: Dict with provider-specific information and debugging data

        Raises:
            ExtractionError: If extraction fails
        """
        pass


class ExtractionError(Exception):
    """Custom exception for extraction errors."""
    pass