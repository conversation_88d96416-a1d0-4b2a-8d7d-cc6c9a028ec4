"""Gemini 2.5 Flash adapter implementing layout-agnostic extraction."""

from __future__ import annotations

import logging
from typing import Any, Dict, Tuple

from .base import ExtractionAdapter
from ..config import settings
from ..extraction import (
    GlobalReasoner,
    Regionizer,
    RegionSemanticClassifier,
)
from ..extraction.confidence import ConfidenceCalculator
from ..extraction.models import DocumentContext
from ..extraction.semantic_classifier import create_semantic_classifier
from ..models import ExtractionResult

logger = logging.getLogger(__name__)


class GeminiFlashAdapter(ExtractionAdapter):
    """Adapter that orchestrates OCR regionization, Gemini classification, and reasoning."""

    def __init__(self) -> None:
        self.regionizer = Regionizer()
        self.classifier: RegionSemanticClassifier = create_semantic_classifier()
        self.reasoner = GlobalReasoner(confidence_calculator=ConfidenceCalculator())

    async def extract(self, document: DocumentContext) -> Tuple[ExtractionResult, float, Dict[str, Any]]:
        logger.info("Running Gemini Flash extraction on document (mime=%s)", document.mime_type)

        ocr = document.ocr
        regions = self.regionizer.build_regions(ocr)
        logger.debug("Regionizer produced %d regions", len(regions))

        classifications = await self.classifier.classify(regions)
        reasoned = self.reasoner.assemble(ocr, classifications)

        extraction = reasoned.extraction
        confidence = extraction.confidence or 0.0

        metadata: Dict[str, Any] = {
            "provider": "gemini_flash",
            "region_count": len(regions),
            "classified_regions": len(classifications),
            "gemini_calls": sum(1 for c in classifications if c.source == "gemini"),
            "heuristic_only": sum(1 for c in classifications if c.source == "heuristic"),
            "document_confidence": confidence,
            "fields_confidence": extraction.confidence_breakdown.fields if extraction.confidence_breakdown else {},
            "settings_provider": settings.EXTRACTION_PROVIDER,
        }

        return extraction, confidence, metadata
