"""Stub adapter for testing and development."""

import hashlib
import logging
from typing import Any, Dict, <PERSON><PERSON>

from .base import ExtractionAdapter
from ..extraction.models import DocumentContext
from ..models import (
    ExtractionResult,
    InvoiceInfo,
    LineItem,
    PaymentInstructions,
    SupplierInfo,
    TotalsInfo,
    VatRate,
)

logger = logging.getLogger(__name__)


class StubExtractionAdapter(ExtractionAdapter):
    """Deterministic stub adapter for testing and development."""

    async def extract(self, document: DocumentContext) -> Tuple[ExtractionResult, float, Dict[str, Any]]:
        """Generate deterministic extraction based on OCR text hash."""
        logger.info("Processing with stub adapter")

        text = document.ocr.text
        text_hash = hashlib.sha256(text.encode("utf-8")).hexdigest()[:8]
        hash_int = int(text_hash, 16)

        suppliers = [
            {"name": "ACME Office Supplies", "vat": "BE0123456789", "iban": "****************"},
            {"name": "Green Energy Solutions", "vat": "BE0987654321", "iban": "****************"},
            {"name": "Premium Consulting Services", "vat": "BE0555123456", "iban": "****************"},
        ]
        supplier_data = suppliers[hash_int % len(suppliers)]

        net_value = hash_int % 900 + 100
        net_amount = f"{net_value:.2f}"
        vat_amount = f"{net_value * 0.21:.2f}"
        gross_amount = f"{net_value * 1.21:.2f}"

        extraction_result = ExtractionResult(
            supplier=SupplierInfo(
                name=supplier_data["name"],
                vat=supplier_data["vat"],
                iban=None,
                address="123 Rue de la Test, 1000 Brussels",
            ),
            customer=None,
            invoice=InvoiceInfo(
                number=f"INV-2024-{hash_int % 1000:03d}",
                issue_date="2024-08-26",
                due_date="2024-09-26",
                currency="EUR",
                net=net_amount,
                vat=vat_amount,
                gross=gross_amount,
            ),
            totals=TotalsInfo(
                net=net_amount,
                vat=vat_amount,
                gross=gross_amount,
                currency="EUR",
            ),
            payment_instructions=PaymentInstructions(
                iban=supplier_data["iban"],
                bic="BBRUBEBB",
                structured_ref=None,
            ),
            lines=[
                LineItem(
                    description="Invoice total",
                    quantity="1",
                    unit_price=net_amount,
                    vat_rate=VatRate.STANDARD,
                    account_hint=None,
                )
            ],
        )

        confidence = 0.85 + (hash_int % 15) / 100.0
        metadata = {
            "provider": "stub",
            "text_hash": text_hash,
            "text_length": len(text),
            "deterministic": True,
        }

        return extraction_result, float(min(confidence, 0.99)), metadata
