# -*- coding: utf-8 -*-
"""Gemini + LangExtract adapter for Belgian invoice extraction."""

import asyncio
import textwrap
import logging
from typing import Any, Dict, List, Tuple

import langextract as lx

from .base import ExtractionAdapter, ExtractionError
from ..models import ExtractionResult, SupplierInfo, InvoiceInfo, LineItem, VatRate, ConfidenceBreakdown
from ..config import settings

logger = logging.getLogger(__name__)

# Configuration from settings
GEMINI_MODEL = settings.GEMINI_MODEL
GEMINI_API_KEY = settings.GEMINI_API_KEY
AI_EXTRACTION_TIMEOUT = settings.AI_EXTRACTION_TIMEOUT
AI_EXTRACTION_RETRIES = settings.AI_EXTRACTION_RETRIES

# LangExtract prompt for Belgian invoice extraction
PROMPT = textwrap.dedent("""
Extract Belgian supplier invoice fields from the input TEXT and return ONLY values
that appear in the text (no hallucination). Use exact spans from the source text.

Required fields:
- supplier: {name, vat?, iban?, address?}
- invoice: {number, issueDate, dueDate?, currency='EUR', net, vat, gross}
- lines: list of {description, quantity, unitPrice, vatRate in [21,12,6,0]}

Extraction rules:
- Dates: YYYY-MM-DD format only
- Amounts: decimal with dot separator (e.g., 121.00)
- Net + VAT should approximately equal Gross (±0.01 EUR)
- Supplier VAT should match Belgian formats (BE0XXXXXXXXX or similar)
- If line items are unclear, extract at least one line approximating the totals
- Return extractions grounded to exact spans from source text
- Do not paraphrase or infer information not present in the text
""")

# Few-shot examples to guide LangExtract's extraction format
EXAMPLES = [
    lx.data.ExampleData(
        text=textwrap.dedent("""
        INVOICE 2024-INV-015
        Issue date: 2024-03-05
        Due: 2024-04-04

        Supplier: ACME SPRL
        Address: Rue de la Paix 123, 1000 Brussels, Belgium
        VAT: BE0123456789
        IBAN: BE68 **************

        1 x Cloud services @ 100.00 EUR
        VAT 21%
        Total VAT: 21.00
        TOTAL: 121.00
        """).strip(),
        extractions=[
            lx.data.Extraction(
                extraction_class="supplier",
                extraction_text="ACME SPRL",
                attributes={
                    "name": "ACME SPRL",
                    "vat": "BE0123456789",
                    "iban": "BE68 **************",
                    "address": "Rue de la Paix 123, 1000 Brussels, Belgium"
                }
            ),
            lx.data.Extraction(
                extraction_class="invoice_header",
                extraction_text="INVOICE 2024-INV-015 | Issue date: 2024-03-05 | Due: 2024-04-04",
                attributes={
                    "number": "2024-INV-015",
                    "issueDate": "2024-03-05",
                    "dueDate": "2024-04-04",
                    "currency": "EUR",
                    "net": "100.00",
                    "vat": "21.00",
                    "gross": "121.00"
                }
            ),
            lx.data.Extraction(
                extraction_class="line_item",
                extraction_text="1 x Cloud services @ 100.00 EUR VAT 21%",
                attributes={
                    "description": "Cloud services",
                    "quantity": "1",
                    "unitPrice": "100.00",
                    "vatRate": 21
                }
            )
        ]
    ),
    lx.data.ExampleData(
        text=textwrap.dedent("""
        Factuur: F-2024-0087
        Datum: 15-08-2024
        Vervaldatum: 15-09-2024

        Van: Green Energy Solutions BVBA
        Adres: Industriepark 45, 9000 Gent, België
        BTW: BE0987654321

        Kantoorbenodigdheden: 75.00 EUR (6% BTW)
        Software licentie: 200.00 EUR (21% BTW)

        Subtotaal: 275.00
        BTW 6%: 4.50
        BTW 21%: 42.00
        Totaal: 321.50 EUR
        """).strip(),
        extractions=[
            lx.data.Extraction(
                extraction_class="supplier",
                extraction_text="Green Energy Solutions BVBA",
                attributes={
                    "name": "Green Energy Solutions BVBA",
                    "vat": "BE0987654321",
                    "address": "Industriepark 45, 9000 Gent, België"
                }
            ),
            lx.data.Extraction(
                extraction_class="invoice_header",
                extraction_text="Factuur: F-2024-0087 | Datum: 15-08-2024 | Vervaldatum: 15-09-2024",
                attributes={
                    "number": "F-2024-0087",
                    "issueDate": "2024-08-15",
                    "dueDate": "2024-09-15",
                    "currency": "EUR",
                    "net": "275.00",
                    "vat": "46.50",
                    "gross": "321.50"
                }
            ),
            lx.data.Extraction(
                extraction_class="line_item",
                extraction_text="Kantoorbenodigdheden: 75.00 EUR (6% BTW)",
                attributes={
                    "description": "Kantoorbenodigdheden",
                    "quantity": "1",
                    "unitPrice": "75.00",
                    "vatRate": 6
                }
            ),
            lx.data.Extraction(
                extraction_class="line_item",
                extraction_text="Software licentie: 200.00 EUR (21% BTW)",
                attributes={
                    "description": "Software licentie",
                    "quantity": "1",
                    "unitPrice": "200.00",
                    "vatRate": 21
                }
            )
        ]
    ),
    lx.data.ExampleData(
        text=textwrap.dedent("""
        FACTUUR 2025-WEB-456
        Datum: 12-07-2025
        Vervaldatum: 12-08-2025

        WebDesign Studio BVBA
        Nieuwstraat 15-17
        2000 Antwerpen
        België
        BTW: BE0444555666

        Website ontwikkeling: 1 x 1200.00 EUR (21% BTW)
        Hosting jaar 1: 1 x 120.00 EUR (21% BTW)

        Subtotaal: 1320.00 EUR
        BTW 21%: 277.20 EUR
        Totaal: 1597.20 EUR
        """).strip(),
        extractions=[
            lx.data.Extraction(
                extraction_class="supplier",
                extraction_text="WebDesign Studio BVBA",
                attributes={
                    "name": "WebDesign Studio BVBA",
                    "vat": "BE0444555666",
                    "address": "Nieuwstraat 15-17, 2000 Antwerpen, België"
                }
            ),
            lx.data.Extraction(
                extraction_class="invoice_header",
                extraction_text="FACTUUR 2025-WEB-456 | Datum: 12-07-2025 | Vervaldatum: 12-08-2025",
                attributes={
                    "number": "2025-WEB-456",
                    "issueDate": "2025-07-12",
                    "dueDate": "2025-08-12",
                    "currency": "EUR",
                    "net": "1320.00",
                    "vat": "277.20",
                    "gross": "1597.20"
                }
            ),
            lx.data.Extraction(
                extraction_class="line_item",
                extraction_text="Website ontwikkeling: 1 x 1200.00 EUR (21% BTW)",
                attributes={
                    "description": "Website ontwikkeling",
                    "quantity": "1",
                    "unitPrice": "1200.00",
                    "vatRate": 21
                }
            ),
            lx.data.Extraction(
                extraction_class="line_item",
                extraction_text="Hosting jaar 1: 1 x 120.00 EUR (21% BTW)",
                attributes={
                    "description": "Hosting jaar 1",
                    "quantity": "1",
                    "unitPrice": "120.00",
                    "vatRate": 21
                }
            )
        ]
    )
]


class GeminiLangExtractAdapter(ExtractionAdapter):
    """Gemini-powered extraction adapter using LangExtract for structured extraction."""

    def __init__(self, model_id: str = GEMINI_MODEL):
        """Initialize Gemini adapter with specified model."""
        self.model_id = model_id
        logger.info(f"Initialized GeminiLangExtractAdapter with model: {model_id}")

    async def extract(self, text: str) -> Tuple[ExtractionResult, float, Dict[str, Any]]:
        """
        Extract structured data using Gemini via LangExtract.

        Args:
            text: UTF-8 text content from document

        Returns:
            Tuple of (ExtractionResult, confidence, metadata)
        """
        if not text.strip():
            raise ExtractionError("Empty text provided for extraction")

        try:
            logger.info(f"Starting extraction with Gemini model: {self.model_id}")
            
            # Run LangExtract with Gemini
            extract_kwargs = {
                "text_or_documents": text,
                "prompt_description": PROMPT,
                "examples": EXAMPLES,
                "model_id": self.model_id
            }

            # Add API key if available
            if GEMINI_API_KEY:
                extract_kwargs["api_key"] = GEMINI_API_KEY

            result = await asyncio.to_thread(self._run_langextract, extract_kwargs)
            
            logger.info(f"LangExtract returned {len(result.extractions)} extractions")
            
            # Map LangExtract's generic extractions to our Pydantic models
            supplier_data = {}
            header_data = {}
            lines_data: List[Dict[str, Any]] = []
            
            for ext in result.extractions:
                cls = ext.extraction_class
                attrs = ext.attributes or {}
                
                if cls == "supplier":
                    supplier_data = {
                        "name": attrs.get("name") or ext.extraction_text.strip(),
                        "vat": self._clean_vat_number(attrs.get("vat")),
                        "iban": self._clean_iban(attrs.get("iban")),
                        "address": attrs.get("address")
                    }
                    
                elif cls == "invoice_header":
                    header_data = {
                        "number": attrs.get("number"),
                        "issue_date": self._normalize_date(attrs.get("issueDate")),
                        "due_date": self._normalize_date(attrs.get("dueDate")),
                        "currency": attrs.get("currency") or "EUR",
                        "net": self._normalize_amount(attrs.get("net")),
                        "vat": self._normalize_amount(attrs.get("vat")),
                        "gross": self._normalize_amount(attrs.get("gross")),
                    }
                    
                elif cls == "line_item":
                    vat_rate = self._normalize_vat_rate(attrs.get("vatRate"))
                    if vat_rate is not None:
                        lines_data.append({
                            "description": attrs.get("description") or ext.extraction_text[:80].strip(),
                            "quantity": self._normalize_amount(attrs.get("quantity")) or "1",
                            "unit_price": self._normalize_amount(attrs.get("unitPrice")) or "0.00",
                            "vat_rate": vat_rate
                        })

            # Fallback date extraction if LangExtract missed required header fields
            if text and not header_data.get("issue_date"):
                fallback_issue_date = self._extract_first_date(text)
                if fallback_issue_date:
                    header_data["issue_date"] = fallback_issue_date

            # Create fallback line if no lines extracted
            if not lines_data and header_data.get("net") and header_data.get("vat"):
                try:
                    net = float(header_data["net"])
                    vat = float(header_data["vat"])
                    # Estimate VAT rate from amounts
                    if net > 0:
                        rate_pct = round((vat / net) * 100)
                        if rate_pct in [21, 12, 6, 0]:
                            lines_data.append({
                                "description": "Invoice total",
                                "quantity": "1",
                                "unit_price": header_data["net"],
                                "vat_rate": VatRate(rate_pct)
                            })
                except (ValueError, ZeroDivisionError):
                    pass

            # Build ExtractionResult
            extraction_result = ExtractionResult(
                supplier=SupplierInfo(**{k: v for k, v in supplier_data.items() if v}),
                invoice=InvoiceInfo(**{k: v for k, v in header_data.items() if v}),
                lines=[LineItem(**line) for line in lines_data]
            )
            
            # Calculate confidence score
            confidence = self._calculate_confidence(extraction_result, result.extractions)
            
            # Build metadata for debugging
            metadata = {
                "provider": "gemini_langextract",
                "model": self.model_id,
                "extraction_count": len(result.extractions),
                "grounded_count": sum(1 for e in result.extractions 
                                    if hasattr(e, 'start_char') and e.start_char is not None),
                "text_length": len(text),
                "field_coverage": self._get_field_coverage(extraction_result)
            }
            
            # Add visualization if available for debugging
            try:
                viz_result = lx.visualize(result)
                if hasattr(viz_result, 'data'):
                    metadata["viz_html"] = viz_result.data
            except Exception as e:
                logger.warning(f"Could not generate visualization: {e}")
            
            logger.info(f"Extraction completed with confidence: {confidence:.3f}")
            return extraction_result, confidence, metadata
            
        except asyncio.CancelledError:
            logger.warning("Gemini extraction cancelled by caller")
            raise
        except Exception as e:
            logger.error(f"Gemini extraction failed: {str(e)}")
            raise ExtractionError(f"Gemini extraction failed: {str(e)}") from e

    def _clean_vat_number(self, vat: str) -> str:
        """Clean and validate Belgian VAT number format."""
        if not vat:
            return None

        # Remove spaces and normalize
        raw = str(vat).strip()
        if not raw:
            return None

        cleaned = ''.join(raw.split()).upper()

        # Validate Belgian VAT format (BE + 10 digits)
        if cleaned.startswith('BE') and len(cleaned) == 12 and cleaned[2:].isdigit():
            return cleaned

        return raw  # Return original if doesn't match expected format

    def _clean_iban(self, iban: str) -> str:
        """Clean IBAN format."""
        if not iban:
            return None

        # Remove spaces and normalize
        raw = str(iban).strip()
        if not raw:
            return None

        cleaned = ''.join(raw.split()).upper()

        # Basic IBAN validation (starts with 2 letters, then digits)
        if len(cleaned) >= 15 and cleaned[:2].isalpha() and cleaned[2:4].isdigit():
            # Add spaces for readability: BE12 3456 7890 1234
            if cleaned.startswith('BE') and len(cleaned) == 16:
                return f"{cleaned[:4]} {cleaned[4:8]} {cleaned[8:12]} {cleaned[12:]}"

        return raw  # Return original if doesn't match expected format

    def _normalize_date(self, date_str: str) -> str:
        """Normalize date to YYYY-MM-DD format."""
        if not date_str:
            return None
        
        # Handle common European formats: DD-MM-YYYY, DD/MM/YYYY
        import re
        
        # Already in YYYY-MM-DD format
        if re.match(r'^\d{4}-\d{2}-\d{2}$', date_str):
            return date_str
            
        # DD-MM-YYYY or DD/MM/YYYY format
        match = re.match(r'^(\d{1,2})[-/](\d{1,2})[-/](\d{4})$', date_str)
        if match:
            day, month, year = match.groups()
            return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
        
        return None

    def _normalize_amount(self, amount_str: str) -> str:
        """Normalize amount to decimal string format."""
        if not amount_str:
            return None

        # Remove currency symbols and spaces
        import re
        cleaned = re.sub(r'[€$£\s]', '', str(amount_str))

        # Replace comma with dot for decimal separator
        cleaned = cleaned.replace(',', '.')

        try:
            # Validate it's a valid number
            float(cleaned)
            return cleaned
        except ValueError:
            return None

    def _normalize_vat_rate(self, vat_rate: Any) -> VatRate | None:
        """Normalize VAT rate values emitted by LangExtract."""
        if vat_rate is None:
            return None

        if isinstance(vat_rate, VatRate):
            return vat_rate

        try:
            cleaned = str(vat_rate).strip()
            if not cleaned:
                return None

            cleaned = cleaned.replace('%', '').replace(',', '.').replace(' ', '')
            value = float(cleaned)
        except (TypeError, ValueError):
            return None

        value_int = int(round(value))
        if value_int in (21, 12, 6, 0):
            return VatRate(value_int)

        return None

    def _calculate_confidence(self, extraction: ExtractionResult, langextract_results: List) -> float:
        """Calculate confidence score based on extraction quality."""
        
        # Base confidence factors
        coverage_score = 0.0
        grounding_score = 0.0  
        arithmetic_score = 0.0
        
        # 1. Field coverage (40% weight)
        required_fields = ["number", "issue_date", "net", "vat", "gross"]
        supplier_fields = ["name"]
        
        invoice_coverage = sum(1 for field in required_fields 
                             if getattr(extraction.invoice, field, None)) / len(required_fields)
        supplier_coverage = 1.0 if extraction.supplier.name else 0.0
        lines_coverage = 1.0 if extraction.lines else 0.0
        
        coverage_score = (invoice_coverage + supplier_coverage + lines_coverage) / 3
        
        # 2. Grounding quality (40% weight)
        if langextract_results:
            grounded_extractions = sum(1 for e in langextract_results 
                                     if hasattr(e, 'start_char') and e.start_char is not None)
            grounding_score = grounded_extractions / len(langextract_results)
        
        # 3. Arithmetic consistency (20% weight)
        try:
            if extraction.invoice.net and extraction.invoice.vat and extraction.invoice.gross:
                net = float(extraction.invoice.net)
                vat = float(extraction.invoice.vat)
                gross = float(extraction.invoice.gross)
                
                # Check if net + vat ≈ gross (within 1 cent tolerance)
                if abs((net + vat) - gross) <= 0.01:
                    arithmetic_score = 1.0
                elif abs((net + vat) - gross) <= 0.10:  # Within 10 cents
                    arithmetic_score = 0.5
        except (ValueError, TypeError):
            pass
        
        # Weighted final score
        final_confidence = (
            0.4 * coverage_score +
            0.4 * grounding_score + 
            0.2 * arithmetic_score
        )
        
        # Ensure confidence is between 0 and 1
        return max(0.0, min(1.0, final_confidence))

    def _get_field_coverage(self, extraction: ExtractionResult) -> Dict[str, bool]:
        """Get field coverage breakdown for debugging."""
        return {
            "supplier_name": bool(extraction.supplier.name),
            "supplier_vat": bool(extraction.supplier.vat),
            "invoice_number": bool(extraction.invoice.number),
            "invoice_date": bool(extraction.invoice.issue_date),
            "invoice_amounts": all([extraction.invoice.net, 
                                  extraction.invoice.vat, 
                                  extraction.invoice.gross]),
            "has_lines": bool(extraction.lines),
            "line_count": len(extraction.lines) if extraction.lines else 0
        }

    def _extract_first_date(self, text: str) -> str | None:
        """Best-effort date extraction from raw document text."""
        import re

        if not text:
            return None

        patterns = [r"\d{4}-\d{2}-\d{2}", r"\d{1,2}[/-]\d{1,2}[/-]\d{4}"]
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                normalized = self._normalize_date(match.group(0))
                if normalized:
                    return normalized
        return None

    def _run_langextract(self, extract_kwargs: Dict[str, Any]):
        """Invoke LangExtract synchronously (for execution in a worker thread)."""
        return lx.extract(**extract_kwargs)
