"""Enhanced document extraction service with provider switching and shadow mode."""

import asyncio
import logging
from typing import Any, Dict, Optional, Tuple

from ..adapters import Extraction<PERSON>dapter, GeminiFlashAdapter, StubExtractionAdapter
from ..extraction.models import DocumentContext
from ..adapters.base import ExtractionError
from ..config import settings
from ..models import ExtractionResult

logger = logging.getLogger(__name__)


class ExtractedDocument:
    """Container for extraction results with metadata."""
    
    def __init__(
        self,
        primary_result: ExtractionResult,
        primary_confidence: float,
        primary_metadata: Dict[str, Any],
        shadow_result: Optional[ExtractionResult] = None,
        shadow_confidence: Optional[float] = None,
        shadow_metadata: Optional[Dict[str, Any]] = None,
        text_length: int = 0,
        processing_time: float = 0.0
    ):
        self.primary_result = primary_result
        self.primary_confidence = primary_confidence
        self.primary_metadata = primary_metadata
        self.shadow_result = shadow_result
        self.shadow_confidence = shadow_confidence
        self.shadow_metadata = shadow_metadata
        self.text_length = text_length
        self.processing_time = processing_time


class EnhancedExtractionService:
    """Enhanced extraction service with provider switching and shadow mode support."""

    def __init__(self, entity_id: int):
        """Initialize service for specific entity."""
        self.entity_id = entity_id
        self.provider = settings.EXTRACTION_PROVIDER
        self.run_mode = settings.AI_EXTRACT_RUN_MODE
        
        # Initialize adapters
        self.primary_adapter = self._create_adapter(self.provider)
        self.shadow_adapter = self._create_adapter("stub") if self.provider != "stub" else None
        
        logger.info(f"Initialized extraction service for entity {entity_id} "
                   f"with provider={self.provider}, mode={self.run_mode}")

    def _create_adapter(self, provider: str) -> ExtractionAdapter:
        """Create adapter instance for specified provider."""
        if provider in {"gemini_langextract", "gemini_flash"}:
            return GeminiFlashAdapter()
        elif provider == "stub":
            return StubExtractionAdapter()
        else:
            raise ValueError(f"Unknown extraction provider: {provider}")

    async def process_document(
        self,
        document: DocumentContext,
        entity_id: int,
        supplier_templates: Optional[Dict[str, Any]] = None
    ) -> ExtractedDocument:
        """
        Process document text with extraction and optional shadow mode.

        Args:
            text: UTF-8 text content from document
            entity_id: Entity ID for template lookup
            supplier_templates: Optional pre-loaded supplier templates

        Returns:
            ExtractedDocument with primary and optional shadow results
        """
        import time
        start_time = time.time()

        if not document.ocr.text.strip():
            raise ExtractionError("Empty text provided for extraction")

        logger.info(
            "Processing document for entity %s (%s chars, provider=%s, mode=%s)",
            entity_id,
            len(document.ocr.text),
            self.provider,
            self.run_mode,
        )

        try:
            # Primary extraction
            primary_result, primary_confidence, primary_metadata = await self._extract_with_retry(
                self.primary_adapter, document
            )
            
            # Enhance with supplier templates
            enhanced_primary = self._enhance_with_templates(
                primary_result, supplier_templates
            )
            
            # Shadow extraction (if enabled)
            shadow_result = None
            shadow_confidence = None
            shadow_metadata = None
            
            if self.run_mode == "shadow" and self.shadow_adapter:
                try:
                    shadow_result, shadow_confidence, shadow_metadata = await self._extract_with_retry(
                        self.shadow_adapter, document
                    )
                    shadow_result = self._enhance_with_templates(
                        shadow_result, supplier_templates
                    )
                    logger.info(f"Shadow extraction completed with confidence: {shadow_confidence:.3f}")
                except Exception as e:
                    logger.warning(f"Shadow extraction failed, continuing with primary: {e}")

            processing_time = time.time() - start_time
            
            logger.info(f"Document processing completed in {processing_time:.2f}s "
                       f"(primary confidence: {primary_confidence:.3f})")

            return ExtractedDocument(
                primary_result=enhanced_primary,
                primary_confidence=primary_confidence,
                primary_metadata=primary_metadata,
                shadow_result=shadow_result,
                shadow_confidence=shadow_confidence,
                shadow_metadata=shadow_metadata,
                text_length=len(document.ocr.text),
                processing_time=processing_time
            )

        except Exception as e:
            logger.error(f"Document processing failed: {str(e)}")
            raise ExtractionError(f"Document processing failed: {str(e)}") from e

    async def _extract_with_retry(
        self,
        adapter: ExtractionAdapter,
        document: DocumentContext
    ) -> Tuple[ExtractionResult, float, Dict[str, Any]]:
        """Extract with retry logic and timeout."""
        last_exception = None
        
        for attempt in range(settings.AI_EXTRACTION_RETRIES + 1):
            try:
                # Apply timeout to extraction
                result = await asyncio.wait_for(
                    adapter.extract(document),
                    timeout=settings.AI_EXTRACTION_TIMEOUT
                )
                return result

            except asyncio.TimeoutError as e:
                last_exception = e
                logger.warning(f"Extraction attempt {attempt + 1} timed out")
                if attempt < settings.AI_EXTRACTION_RETRIES:
                    # Exponential backoff
                    await asyncio.sleep(2 ** attempt)

            except Exception as e:
                last_exception = e
                logger.warning(f"Extraction attempt {attempt + 1} failed: {str(e)}")
                if attempt < settings.AI_EXTRACTION_RETRIES:
                    await asyncio.sleep(2 ** attempt)

        # All retries exhausted
        raise ExtractionError(f"Extraction failed after {settings.AI_EXTRACTION_RETRIES + 1} attempts: {last_exception}")

    def _enhance_with_templates(
        self,
        result: ExtractionResult,
        supplier_templates: Optional[Dict[str, Any]]
    ) -> ExtractionResult:
        """Enhance extraction with supplier template hints."""
        if not supplier_templates or not result.supplier.vat:
            return result

        template = supplier_templates.get(result.supplier.vat)
        if not template:
            return result

        logger.info(f"Found supplier template for VAT {result.supplier.vat}")

        # Update account hints for all lines
        enhanced_lines = []
        for line in result.lines:
            if not hasattr(line, 'account_hint') or not line.account_hint:
                if template.get("default_account_id"):
                    # Create new line item with account hint
                    line_dict = line.model_dump()
                    line_dict['account_hint'] = template["default_account_id"]
                    enhanced_lines.append(type(line)(**line_dict))
                else:
                    enhanced_lines.append(line)
            else:
                enhanced_lines.append(line)

        # Return new result with enhanced lines
        result_dict = result.model_dump()
        result_dict['lines'] = enhanced_lines
        return ExtractionResult(**result_dict)

    def should_auto_suggest(self, confidence: float) -> bool:
        """Determine if extraction quality is sufficient for auto-suggestion."""
        return confidence >= settings.AI_CONFIDENCE_THRESHOLD

    def get_extraction_summary(self, doc: ExtractedDocument) -> Dict[str, Any]:
        """Get summary of extraction for logging/debugging."""
        summary = {
            "entity_id": self.entity_id,
            "provider": self.provider,
            "run_mode": self.run_mode,
            "processing_time": doc.processing_time,
            "text_length": doc.text_length,
            "primary": {
                "confidence": doc.primary_confidence,
                "provider": doc.primary_metadata.get("provider", "unknown"),
                "has_supplier": bool(doc.primary_result.supplier.name),
                "has_invoice": bool(doc.primary_result.invoice.number),
                "line_count": len(doc.primary_result.lines),
                "can_auto_suggest": self.should_auto_suggest(doc.primary_confidence)
            }
        }
        
        if doc.shadow_result:
            summary["shadow"] = {
                "confidence": doc.shadow_confidence,
                "provider": doc.shadow_metadata.get("provider", "unknown"),
                "has_supplier": bool(doc.shadow_result.supplier.name),
                "has_invoice": bool(doc.shadow_result.invoice.number),
                "line_count": len(doc.shadow_result.lines)
            }
        
        return summary


def create_extraction_service(entity_id: int) -> EnhancedExtractionService:
    """
    Factory function to create extraction service for entity.

    Args:
        entity_id: Entity ID for scoped service

    Returns:
        Configured EnhancedExtractionService
    """
    return EnhancedExtractionService(entity_id)