"""Text extraction utilities for documents producing OCR tokens and regions."""

from __future__ import annotations

import logging
import tempfile
from collections import defaultdict
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import magic

from ..extraction.models import BoundingBox, DocumentPage, OcrLine, OcrResult, OcrToken

logger = logging.getLogger(__name__)


class TextExtractionError(Exception):
    """Exception raised during text extraction."""


class TextExtractionService:
    """Service to extract OCR tokens from various document formats."""

    def __init__(self) -> None:
        self.mime = magic.Magic(mime=True)

    async def extract_text_from_bytes(
        self,
        file_bytes: bytes,
        mime_type: Optional[str] = None,
    ) -> Tuple[OcrResult, str]:
        """Extract OCR structures from document bytes."""
        if not file_bytes:
            raise TextExtractionError("Empty file bytes provided")

        if not mime_type:
            mime_type = self._detect_mime_type(file_bytes)

        logger.info("Extracting OCR tokens from %s bytes (%s)", len(file_bytes), mime_type)

        file_extension = self._get_file_extension(mime_type)
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            temp_file.write(file_bytes)
            temp_path = Path(temp_file.name)

        try:
            if mime_type == "application/pdf":
                ocr = await self._ocr_pdf(temp_path)
            elif mime_type.startswith("image/"):
                ocr = await self._ocr_single_image(temp_path)
            else:
                raise TextExtractionError(f"Unsupported MIME type: {mime_type}")

            if not ocr.text.strip():
                raise TextExtractionError("OCR produced empty text")

            return ocr, mime_type
        finally:
            temp_path.unlink(missing_ok=True)

    def _detect_mime_type(self, file_bytes: bytes) -> str:
        try:
            return self.mime.from_buffer(file_bytes)
        except Exception as exc:  # noqa: BLE001
            logger.warning("MIME type detection failed: %s", exc)
            return "application/octet-stream"

    async def _ocr_pdf(self, file_path: Path) -> OcrResult:
        try:
            from pdf2image import convert_from_path
        except ImportError as exc:  # noqa: BLE001
            raise TextExtractionError("pdf2image not installed for PDF OCR") from exc

        try:
            images = convert_from_path(str(file_path), first_page=1, last_page=10)
        except Exception as exc:  # noqa: BLE001
            raise TextExtractionError(f"PDF conversion failed: {exc}") from exc

        if not images:
            raise TextExtractionError("No pages rendered from PDF")

        return await self._ocr_images(images)

    async def _ocr_single_image(self, file_path: Path) -> OcrResult:
        try:
            from PIL import Image
        except ImportError as exc:  # noqa: BLE001
            raise TextExtractionError("Pillow not installed for image OCR") from exc

        image = Image.open(file_path)
        return await self._ocr_images([image])

    async def _ocr_images(self, images) -> OcrResult:  # type: ignore[override]
        try:
            import pytesseract
            from pytesseract import Output
        except ImportError as exc:  # noqa: BLE001
            raise TextExtractionError("pytesseract not installed") from exc

        tokens: List[OcrToken] = []
        lines: List[OcrLine] = []
        text_lines: List[str] = []
        pages: List[DocumentPage] = []

        for page_index, image in enumerate(images, start=1):
            prepared = self._prepare_image(image)
            normalized = self._normalize_orientation(prepared, pytesseract)
            page = DocumentPage.from_image(page_index, normalized)
            pages.append(page)

            data = pytesseract.image_to_data(
                normalized, lang="eng+fra+nld", output_type=Output.DICT
            )
            page_tokens, page_lines, page_texts = self._parse_ocr_data(
                data, page_index, normalized.width, normalized.height
            )
            tokens.extend(page_tokens)
            lines.extend(page_lines)
            text_lines.extend(page_texts)

        document_text = "\n".join(text_lines)
        return OcrResult(text=document_text, tokens=tokens, lines=lines, pages=pages)

    @staticmethod
    def _prepare_image(image):
        if image.mode != "RGB":
            return image.convert("RGB")
        return image

    @staticmethod
    def _normalize_orientation(image, pytesseract):
        try:
            osd = pytesseract.image_to_osd(image)
            for line in osd.splitlines():
                if line.lower().startswith("rotate"):
                    rotation = int(line.split(":")[1].strip())
                    if rotation:
                        return image.rotate(-rotation, expand=True)
        except Exception:  # noqa: BLE001
            pass
        return image

    def _parse_ocr_data(
        self,
        data: Dict[str, List],
        page_number: int,
        width: int,
        height: int,
    ) -> Tuple[List[OcrToken], List[OcrLine], List[str]]:
        line_map: Dict[str, Dict[str, List]] = defaultdict(
            lambda: {"tokens": [], "left": [], "top": [], "right": [], "bottom": [], "texts": []}
        )

        tokens: List[OcrToken] = []
        lines: List[OcrLine] = []
        text_lines: List[str] = []

        entries = len(data.get("text", []))
        for idx in range(entries):
            text_value = (data["text"][idx] or "").strip()
            page = data["page_num"][idx]
            level = data["level"][idx]
            if page != page_number:
                continue

            line_key = f"{page}_{data['block_num'][idx]}_{data['par_num'][idx]}_{data['line_num'][idx]}"
            line_entry = line_map[line_key]

            if level == 5 and text_value:
                left = data["left"][idx]
                top = data["top"][idx]
                width_box = data["width"][idx]
                height_box = data["height"][idx]
                confidence = data["conf"][idx]

                bbox = BoundingBox(
                    x0=max(0.0, left / width),
                    y0=max(0.0, top / height),
                    x1=min(1.0, (left + width_box) / width),
                    y1=min(1.0, (top + height_box) / height),
                )
                token_id = f"p{page_number}_t{idx}"
                line_entry["tokens"].append(token_id)
                line_entry["left"].append(left)
                line_entry["top"].append(top)
                line_entry["right"].append(left + width_box)
                line_entry["bottom"].append(top + height_box)
                line_entry["texts"].append(text_value)

                if isinstance(confidence, str):
                    try:
                        confidence = float(confidence)
                    except ValueError:
                        confidence = -1

                tokens.append(
                    OcrToken(
                        id=token_id,
                        text=text_value,
                        bbox=bbox,
                        confidence=max(0.0, min(1.0, float(confidence) / 100.0)) if isinstance(confidence, (int, float)) else 0.0,
                        page=page_number,
                        line_id=line_key,
                    )
                )

        for line_key, entry in line_map.items():
            if not entry["tokens"]:
                continue
            bbox = BoundingBox(
                x0=max(0.0, min(entry["left"]) / width),
                y0=max(0.0, min(entry["top"]) / height),
                x1=min(1.0, max(entry["right"]) / width),
                y1=min(1.0, max(entry["bottom"]) / height),
            )
            text_line = " ".join(entry["texts"])
            lines.append(
                OcrLine(
                    id=line_key,
                    text=text_line,
                    bbox=bbox,
                    page=page_number,
                    token_ids=list(entry["tokens"]),
                )
            )
            text_lines.append(text_line)

        return tokens, lines, text_lines

    def _get_file_extension(self, mime_type: str) -> str:
        mapping = {
            "application/pdf": ".pdf",
            "image/png": ".png",
            "image/jpeg": ".jpg",
            "image/jpg": ".jpg",
            "image/tiff": ".tiff",
            "image/bmp": ".bmp",
            "image/gif": ".gif",
        }
        return mapping.get(mime_type, "")

    def get_supported_mime_types(self) -> List[str]:
        return [
            "application/pdf",
            "image/png",
            "image/jpeg",
            "image/tiff",
            "image/bmp",
            "image/gif",
        ]

    def is_supported(self, mime_type: str) -> bool:
        return mime_type == "application/pdf" or mime_type.startswith("image/")

