"""Journal suggestion service for converting extractions to balanced accounting entries."""

import logging
from decimal import ROUND_HALF_UP, Decimal
from typing import Any

from ..models import ExtractionResult, Suggestion, SuggestionLine, VatRate

logger = logging.getLogger(__name__)


class SuggestionService:
    """Service to convert extraction results into balanced journal entries."""

    def __init__(self, account_mappings: dict[str, Any] | None = None):
        """
        Initialize suggestion service.

        Args:
            account_mappings: Chart of accounts configuration for the entity
        """
        self.account_mappings = account_mappings or self._default_account_mappings()

    def _default_account_mappings(self) -> dict[str, Any]:
        """Default Belgian chart of accounts mappings."""
        return {
            "expense_account": 6000,  # General expenses
            "vat_receivable": 4510,  # VAT receivable (purchase)
            "accounts_payable": 4400,  # Supplier payables
            "vat_codes": {
                VatRate.STANDARD: 1,  # 21% VAT code
                VatRate.REDUCED: 2,  # 12% VAT code
                VatRate.LOW: 3,  # 6% VAT code
                VatRate.ZERO: 4,  # 0% VAT code
            },
        }

    async def create_suggestion(
        self,
        extraction: ExtractionResult,
        entity_id: int,
        supplier_templates: dict[str, Any] | None = None,
    ) -> Suggestion:
        """
        Create balanced journal suggestion from extraction result.
        Handles real-world scenarios with flexible amount calculation and validation.

        Args:
            extraction: AI extraction result
            entity_id: Entity ID for context
            supplier_templates: Optional supplier account preferences

        Returns:
            Balanced journal suggestion ready for posting
        """
        logger.info(f"Creating suggestion for entity {entity_id}")

        try:
            # Normalize and validate amounts first
            normalized_amounts = self._normalize_invoice_amounts(extraction)
            logger.info(f"Normalized amounts: {normalized_amounts}")

            lines = []

            # Process each invoice line to create expense + VAT entries
            for line_item in extraction.lines:
                # Get account ID from hints or default mapping
                account_id = self._get_expense_account(
                    line_item, extraction.supplier, supplier_templates
                )

                # Calculate amounts with proper rounding
                net_amount = Decimal(line_item.unit_price) * Decimal(line_item.quantity)
                vat_rate_decimal = Decimal(line_item.vat_rate.value) / Decimal("100")
                vat_amount = (net_amount * vat_rate_decimal).quantize(
                    Decimal("0.01"), rounding=ROUND_HALF_UP
                )

                # Add expense line (debit)
                expense_line = SuggestionLine(
                    account_id=account_id,
                    debit=str(net_amount),
                    credit="0",
                    vat_code_id=self.account_mappings["vat_codes"].get(
                        line_item.vat_rate
                    ),
                    memo=line_item.description,
                )
                lines.append(expense_line)

                # Add VAT line if applicable (debit)
                if vat_amount > 0:
                    vat_line = SuggestionLine(
                        account_id=self.account_mappings["vat_receivable"],
                        debit=str(vat_amount),
                        credit="0",
                        memo=f"VAT {line_item.vat_rate.value}% - {line_item.description}",
                    )
                    lines.append(vat_line)

            # Use normalized gross amount for payable line
            gross_amount = normalized_amounts["gross"]
            payable_line = SuggestionLine(
                account_id=self.account_mappings["accounts_payable"],
                debit="0",
                credit=str(gross_amount),
                memo=f"{extraction.supplier.name or 'Supplier'} - {extraction.invoice.number or 'Invoice'}",
            )
            lines.append(payable_line)

            # Auto-balance if needed (common in real-world scenarios)
            lines = self._auto_balance_lines(lines, normalized_amounts)

            # Create the suggestion with flexible validation
            suggestion = self._create_suggestion_with_flexible_validation(
                extraction, lines, normalized_amounts
            )

            logger.info(
                f"Created suggestion with {len(lines)} lines, total: {gross_amount}"
            )
            return suggestion

        except Exception as e:
            logger.error(f"Failed to create suggestion: {str(e)}")
            raise

    def _normalize_invoice_amounts(self, extraction: ExtractionResult) -> dict[str, Decimal]:
        """
        Normalize and calculate missing invoice amounts using real-world logic.

        Priority order:
        1. Use gross amount as source of truth (most reliable)
        2. Calculate missing net/VAT based on gross and line items
        3. Handle zero-VAT scenarios properly
        """
        # Get amounts from extraction, defaulting to 0
        gross_str = extraction.invoice.gross or extraction.totals.gross or "0"
        net_str = extraction.invoice.net or extraction.totals.net or "0"
        vat_str = extraction.invoice.vat or extraction.totals.vat or "0"

        gross = Decimal(gross_str)
        net = Decimal(net_str)
        vat = Decimal(vat_str)

        # Calculate total from line items as validation
        line_total_net = Decimal("0")
        line_total_vat = Decimal("0")

        for line in extraction.lines:
            line_net = Decimal(line.unit_price) * Decimal(line.quantity)
            line_vat_rate = Decimal(line.vat_rate.value) / Decimal("100")
            line_vat = (line_net * line_vat_rate).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

            line_total_net += line_net
            line_total_vat += line_vat

        line_total_gross = line_total_net + line_total_vat

        # Scenario 1: Gross is reliable, net/VAT are missing or wrong
        if gross > 0 and (net == 0 or abs(gross - (net + vat)) > Decimal("0.01")):
            logger.info(f"Using gross amount {gross} as source of truth")

            # If we have line items, use their calculated totals
            if line_total_gross > 0 and abs(gross - line_total_gross) <= Decimal("0.01"):
                return {
                    "gross": gross,
                    "net": line_total_net,
                    "vat": line_total_vat,
                    "source": "line_items"
                }

            # Otherwise, assume zero VAT (common scenario)
            if vat == 0 or abs(vat) < Decimal("0.01"):
                return {
                    "gross": gross,
                    "net": gross,
                    "vat": Decimal("0"),
                    "source": "zero_vat_assumption"
                }

            # Calculate net from gross - VAT
            calculated_net = gross - vat
            return {
                "gross": gross,
                "net": calculated_net,
                "vat": vat,
                "source": "gross_minus_vat"
            }

        # Scenario 2: All amounts are present and consistent
        if abs(gross - (net + vat)) <= Decimal("0.01"):
            return {
                "gross": gross,
                "net": net,
                "vat": vat,
                "source": "extraction_consistent"
            }

        # Scenario 3: Use line items as fallback
        if line_total_gross > 0:
            logger.warning(f"Using line item totals as fallback: net={line_total_net}, vat={line_total_vat}, gross={line_total_gross}")
            return {
                "gross": line_total_gross,
                "net": line_total_net,
                "vat": line_total_vat,
                "source": "line_items_fallback"
            }

        # Scenario 4: Last resort - use what we have
        logger.warning(f"Using extracted amounts as-is: net={net}, vat={vat}, gross={gross}")
        return {
            "gross": gross,
            "net": net,
            "vat": vat,
            "source": "extraction_as_is"
        }

    def _auto_balance_lines(self, lines: list[SuggestionLine], normalized_amounts: dict[str, Decimal]) -> list[SuggestionLine]:
        """
        Auto-balance journal lines to handle real-world discrepancies.
        """
        total_debits = sum(Decimal(line.debit) for line in lines)
        total_credits = sum(Decimal(line.credit) for line in lines)
        difference = total_debits - total_credits

        # If already balanced within tolerance, return as-is
        if abs(difference) <= Decimal("0.01"):
            return lines

        logger.info(f"Auto-balancing lines: debits={total_debits}, credits={total_credits}, difference={difference}")

        # Adjust the payable line to balance (most common scenario)
        for line in lines:
            if line.account_id == self.account_mappings["accounts_payable"] and Decimal(line.credit) > 0:
                current_credit = Decimal(line.credit)
                adjusted_credit = current_credit + difference
                line.credit = str(adjusted_credit)
                logger.info(f"Adjusted payable line from {current_credit} to {adjusted_credit}")
                break

        return lines

    def _create_suggestion_with_flexible_validation(
        self,
        extraction: ExtractionResult,
        lines: list[SuggestionLine],
        normalized_amounts: dict[str, Decimal]
    ) -> Suggestion:
        """
        Create suggestion with flexible validation that doesn't fail on minor imbalances.
        """
        # Check balance before creating suggestion
        total_debits = sum(Decimal(line.debit) for line in lines)
        total_credits = sum(Decimal(line.credit) for line in lines)
        difference = abs(total_debits - total_credits)

        if difference > Decimal("0.01"):
            logger.warning(
                f"Suggestion not perfectly balanced: debits={total_debits}, credits={total_credits}, difference={difference}"
            )
            # Continue anyway - let the user review and adjust

        # Create suggestion with flexible validation
        suggestion = Suggestion(
            journal_date=extraction.invoice.issue_date or "2025-01-01",
            reference=extraction.invoice.number,
            description=self._get_journal_description(extraction),
            lines=lines,
        )

        return suggestion

    def _get_expense_account(
        self, line_item, supplier_info, supplier_templates: dict[str, Any] | None
    ) -> int:
        """
        Determine the best expense account for this line item.

        Priority:
        1. Line item account hint (from AI extraction)
        2. Supplier template default account
        3. Default expense account
        """
        # Use AI account hint if available
        if line_item.account_hint:
            return line_item.account_hint

        # Use supplier template default if available
        if supplier_templates and supplier_info.vat:
            template = supplier_templates.get(supplier_info.vat)
            if template and template.get("default_account_id"):
                return template["default_account_id"]

        # Fall back to default expense account
        return self.account_mappings["expense_account"]

    def _get_journal_description(self, extraction: ExtractionResult) -> str:
        """Generate appropriate journal description."""
        supplier_name = extraction.supplier.name
        invoice_number = extraction.invoice.number

        # Try to create a meaningful description
        if len(extraction.lines) == 1:
            # Single line - use the line description
            line_desc = extraction.lines[0].description
            return f"{line_desc} - {supplier_name}"
        else:
            # Multiple lines - use generic description
            return f"Invoice {invoice_number} - {supplier_name}"


def create_suggestion_service(
    entity_id: int, account_mappings: dict[str, Any] | None = None
) -> SuggestionService:
    """
    Factory function to create suggestion service for an entity.

    Args:
        entity_id: Entity ID (for future entity-specific configurations)
        account_mappings: Optional custom account mappings

    Returns:
        Configured SuggestionService
    """
    # Future: Load entity-specific chart of accounts here
    return SuggestionService(account_mappings)
