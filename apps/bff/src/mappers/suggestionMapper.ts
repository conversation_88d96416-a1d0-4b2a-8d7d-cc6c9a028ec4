import { ExtractionResult, Suggestion, SuggestionLine, Database } from '@belbooks/types'
import { SupabaseClient } from '@supabase/supabase-js'

interface SupplierTemplate {
  default_account_id: number
  default_vat_code_id: number
}

// Extended VAT code interface with purchase_account_id (not in base schema)
interface VatCodeWithPurchaseAccount {
  id: number
  rate: number
  purchase_account_id?: number | null
}

/**
 * Maps extraction result to balanced journal suggestion for posting
 * Follows Belgian accounting standards with proper expense/VAT/payable allocation
 */
export async function extractionToSuggestion(
  supabase: SupabaseClient<Database>,
  entityId: number,
  extraction: ExtractionResult,
  supplierTemplate?: SupplierTemplate
): Promise<Suggestion> {
  // Get default accounts for this entity
  const { data: accounts } = await supabase
    .from('accounts')
    .select('id, code, name, account_type')
    .eq('entity_id', entityId)
    .in('account_type', ['expense', 'accounts_payable'])

  if (!accounts || accounts.length === 0) {
    throw new Error('No expense or payable accounts found for entity')
  }

  // Get VAT codes for rate mapping
  const { data: vatCodesRaw } = await supabase
    .from('vat_codes')
    .select('id, rate')
    .eq('entity_id', entityId)

  const vatCodes = (vatCodesRaw || []).map(vc => ({ ...vc, purchase_account_id: null })) as VatCodeWithPurchaseAccount[]

  if (!vatCodes || vatCodes.length === 0) {
    throw new Error('No VAT codes configured for entity')
  }

  // Find accounts by type
  const expenseAccounts = accounts.filter(a => a.account_type === 'expense')
  const payableAccounts = accounts.filter(a => a.account_type === 'accounts_payable')

  if (expenseAccounts.length === 0) {
    throw new Error('No expense accounts found')
  }
  if (payableAccounts.length === 0) {
    throw new Error('No payable accounts found')
  }

  const lines: SuggestionLine[] = []

  // Process each invoice line
  for (const line of extraction.lines) {
    const lineNet = parseFloat(line.unit_price) * parseFloat(line.quantity)
    const vatRate = line.vat_rate
    const lineVat = lineNet * (vatRate / 100)

    // Determine expense account (use template hint or default)
    let expenseAccountId = line.account_hint || supplierTemplate?.default_account_id
    if (!expenseAccountId || !expenseAccounts.find(a => a.id === expenseAccountId)) {
      // Fall back to first expense account
      expenseAccountId = expenseAccounts[0]?.id
    }

    // Find matching VAT code
    const vatCode = vatCodes.find(vc => vc.rate === vatRate / 100) // DB stores as decimal (0.21)
    if (!vatCode) {
      throw new Error(`No VAT code found for rate ${vatRate}%`)
    }

    // Add expense line (debit)
    lines.push({
      accountId: expenseAccountId!,
      debit: lineNet.toFixed(2),
      credit: '0.00',
      vatCodeId: vatCode.id,
      memo: line.description
    })

    // Add VAT line (debit) if VAT > 0
    if (lineVat > 0) {
      const vatAccountId = vatCode.purchase_account_id
      if (!vatAccountId) {
        throw new Error(`VAT code ${vatRate}% missing purchase_account_id`)
      }

      lines.push({
        accountId: vatAccountId,
        debit: lineVat.toFixed(2),
        credit: '0.00',
        vatCodeId: vatCode.id,
        memo: `VAT ${vatRate}% on ${line.description}`
      })
    }
  }

  const grossSource = extraction.invoice.gross || extraction.totals.gross || '0'
  const grossAmount = parseFloat(grossSource)
  lines.push({
    accountId: payableAccounts[0]?.id || 0,
    debit: '0.00',
    credit: grossAmount.toFixed(2),
    memo: `${extraction.supplier.name ?? 'Supplier'} - Invoice ${extraction.invoice.number ?? ''}`
  })

  const totalDebits = lines.reduce((sum, line) => sum + parseFloat(line.debit), 0)
  const totalCredits = lines.reduce((sum, line) => sum + parseFloat(line.credit), 0)

  if (Math.abs(totalDebits - totalCredits) > 0.01) {
    throw new Error(
      `Journal not balanced: debits ${totalDebits.toFixed(2)} != credits ${totalCredits.toFixed(2)}`
    )
  }

  return {
    journalDate: extraction.invoice.issue_date || extraction.invoice.due_date || new Date().toISOString().slice(0, 10),
    reference: extraction.invoice.number || undefined,
    description: `${extraction.supplier.name ?? 'Supplier'} - Invoice ${extraction.invoice.number ?? ''}`,
    lines
  }
}

/**
 * Validates that suggestion lines are properly balanced
 */
export function validateSuggestionBalance(suggestion: Suggestion): { balanced: boolean; error?: string } {
  const totalDebits = suggestion.lines.reduce((sum, line) =>
    sum + parseFloat(line.debit || '0'), 0)
  const totalCredits = suggestion.lines.reduce((sum, line) =>
    sum + parseFloat(line.credit || '0'), 0)

  const difference = Math.abs(totalDebits - totalCredits)

  if (difference > 0.01) {
    return {
      balanced: false,
      error: `Journal not balanced: debits ${totalDebits.toFixed(2)} != credits ${totalCredits.toFixed(2)} (difference: ${difference.toFixed(2)})`
    }
  }

  return { balanced: true }
}

/**
 * Get or create supplier template for consistent account suggestions
 */
export async function getSupplierTemplate(
  supabase: SupabaseClient<Database>,
  entityId: number,
  supplierVat?: string,
  supplierName?: string
): Promise<SupplierTemplate | null> {

  if (!supplierVat && !supplierName) return null

  let query = supabase
    .from('supplier_templates')
    .select('default_account_id, default_vat_code_id')
    .eq('entity_id', entityId)

  if (supplierVat) {
    query = query.eq('supplier_vat', supplierVat)
  } else if (supplierName) {
    query = query.eq('supplier_name', supplierName)
  }

  const { data } = await query.single()

  return (data as SupplierTemplate) || null
}
